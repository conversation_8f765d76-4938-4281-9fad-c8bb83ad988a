# Tauri 音乐播放器开发方案

## 🎯 核心技术栈

### 前端架构
- **框架**: React 18 + TypeScript + Vite
- **UI库**: shadcn/ui (基于 Radix UI + Tailwind CSS)
- **状态管理**: <PERSON><PERSON><PERSON> (轻量级，适合音频状态)
- **路由**: React Router v6
- **样式**: Tailwind CSS (shadcn/ui 内置)
- **图标**: Lucide React (shadcn/ui 推荐)

### 后端架构 (Tauri)
- **语言**: Rust
- **数据库**: SQLite + tauri-plugin-sql
- **音频处理**: rodio / symphonia
- **文件系统**: tauri-plugin-fs

### 音频相关
- **播放引擎**: HTML5 Audio API
- **音频可视化**: Web Audio API + Canvas
- **波形显示**: WaveSurfer.js
- **格式支持**: MP3, FLAC, AAC, OGG

## 📦 项目依赖配置

### package.json 关键依赖
```json
{
  "dependencies": {
    "@tauri-apps/api": "^1.5.0",
    "@tauri-apps/plugin-sql": "^1.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.20.0",
    "zustand": "^4.4.0",
    "lucide-react": "^0.300.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0",
    "wavesurfer.js": "^7.5.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.2.0",
    "typescript": "^5.3.0",
    "vite": "^5.0.0",
    "tailwindcss": "^3.3.0",
    "autoprefixer": "^10.4.0",
    "postcss": "^8.4.0"
  }
}
```

### Cargo.toml (Tauri 后端)
```toml
[dependencies]
tauri = { version = "1.5", features = ["api-all"] }
tauri-plugin-sql = { version = "1.0", features = ["sqlite"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
rodio = "0.17"
id3 = "1.8"
walkdir = "2.4"
```

## 🎨 shadcn/ui 组件规划

### 核心组件使用
```bash
# 安装 shadcn/ui 基础组件
npx shadcn-ui@latest add button
npx shadcn-ui@latest add card
npx shadcn-ui@latest add input
npx shadcn-ui@latest add slider
npx shadcn-ui@latest add table
npx shadcn-ui@latest add dialog
npx shadcn-ui@latest add dropdown-menu
npx shadcn-ui@latest add tabs
npx shadcn-ui@latest add progress
npx shadcn-ui@latest add scroll-area
npx shadcn-ui@latest add tooltip
```

### 自定义音乐组件 (基于 shadcn/ui)
```typescript
// 音频进度条组件
interface AudioSliderProps {
  value: number;
  max: number;
  onChange: (value: number) => void;
}

const AudioSlider = ({ value, max, onChange }: AudioSliderProps) => {
  return (
    <Slider
      value={[value]}
      max={max}
      step={1}
      onValueChange={(values) => onChange(values[0])}
      className="flex-1"
    />
  );
};

// 音量控制组件
const VolumeControl = () => {
  const [volume, setVolume] = useState(50);
  
  return (
    <div className="flex items-center gap-2">
      <Volume2 className="h-4 w-4" />
      <Slider
        value={[volume]}
        max={100}
        step={1}
        onValueChange={(values) => setVolume(values[0])}
        className="w-24"
      />
    </div>
  );
};
```

## 🗄️ 数据库设计

### SQLite 表结构
```sql
-- 歌曲信息表
CREATE TABLE songs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    artist TEXT,
    album TEXT,
    genre TEXT,
    year INTEGER,
    duration INTEGER, -- 秒
    file_path TEXT UNIQUE NOT NULL,
    file_size INTEGER,
    bit_rate INTEGER,
    sample_rate INTEGER,
    cover_art_path TEXT,
    date_added DATETIME DEFAULT CURRENT_TIMESTAMP,
    date_modified DATETIME,
    play_count INTEGER DEFAULT 0,
    last_played DATETIME
);

-- 播放列表表
CREATE TABLE playlists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    cover_image TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    song_count INTEGER DEFAULT 0
);

-- 播放列表歌曲关联表
CREATE TABLE playlist_songs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    playlist_id INTEGER NOT NULL,
    song_id INTEGER NOT NULL,
    position INTEGER NOT NULL,
    added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE,
    UNIQUE(playlist_id, song_id)
);

-- 用户设置表
CREATE TABLE settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 播放历史表
CREATE TABLE play_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    song_id INTEGER NOT NULL,
    played_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    play_duration INTEGER, -- 播放了多少秒
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE
);
```

## 📁 项目结构

```
tauri-music-player/
├── src-tauri/                    # Rust 后端
│   ├── src/
│   │   ├── commands/            # Tauri 命令
│   │   │   ├── audio.rs        # 音频相关命令
│   │   │   ├── database.rs     # 数据库操作
│   │   │   ├── file_system.rs  # 文件系统操作
│   │   │   └── mod.rs
│   │   ├── models/             # 数据模型
│   │   │   ├── song.rs
│   │   │   ├── playlist.rs
│   │   │   └── mod.rs
│   │   ├── utils/              # 工具函数
│   │   │   ├── metadata.rs     # 音频元数据解析
│   │   │   └── mod.rs
│   │   ├── database.rs         # 数据库初始化
│   │   └── main.rs
│   ├── icons/                  # 应用图标
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                        # React 前端
│   ├── components/             # UI 组件
│   │   ├── ui/                # shadcn/ui 组件
│   │   ├── audio/             # 音频相关组件
│   │   │   ├── AudioPlayer.tsx
│   │   │   ├── PlaybackControls.tsx
│   │   │   ├── VolumeControl.tsx
│   │   │   ├── ProgressBar.tsx
│   │   │   └── Visualizer.tsx
│   │   ├── library/           # 音乐库组件
│   │   │   ├── SongList.tsx
│   │   │   ├── AlbumGrid.tsx
│   │   │   ├── ArtistList.tsx
│   │   │   └── SearchBar.tsx
│   │   ├── playlist/          # 播放列表组件
│   │   │   ├── PlaylistManager.tsx
│   │   │   ├── PlaylistCard.tsx
│   │   │   └── CreatePlaylist.tsx
│   │   └── layout/            # 布局组件
│   │       ├── Sidebar.tsx
│   │       ├── Header.tsx
│   │       └── MainLayout.tsx
│   ├── hooks/                 # 自定义 Hooks
│   │   ├── useAudioPlayer.ts
│   │   ├── usePlaylist.ts
│   │   ├── useMusicLibrary.ts
│   │   └── useSettings.ts
│   ├── store/                 # Zustand 状态管理
│   │   ├── audioStore.ts
│   │   ├── libraryStore.ts
│   │   ├── playlistStore.ts
│   │   └── settingsStore.ts
│   ├── types/                 # TypeScript 类型定义
│   │   ├── audio.ts
│   │   ├── playlist.ts
│   │   └── database.ts
│   ├── utils/                 # 工具函数
│   │   ├── tauri.ts          # Tauri API 封装
│   │   ├── audio.ts          # 音频工具函数
│   │   ├── format.ts         # 格式化函数
│   │   └── storage.ts        # 本地存储
│   ├── pages/                # 页面组件
│   │   ├── Library.tsx
│   │   ├── Playlists.tsx
│   │   ├── Settings.tsx
│   │   └── NowPlaying.tsx
│   ├── lib/                  # shadcn/ui 配置
│   │   └── utils.ts
│   ├── App.tsx
│   ├── main.tsx
│   └── index.css
├── components.json           # shadcn/ui 配置
├── tailwind.config.js       # Tailwind 配置
├── tsconfig.json           # TypeScript 配置
├── vite.config.ts          # Vite 配置
└── package.json
```

## 🔧 核心功能实现

### 1. Zustand 状态管理

```typescript
// store/audioStore.ts
interface AudioState {
  currentSong: Song | null;
  isPlaying: boolean;
  volume: number;
  currentTime: number;
  duration: number;
  playMode: 'sequence' | 'repeat' | 'shuffle';
  queue: Song[];
  currentIndex: number;
}

interface AudioActions {
  playSong: (song: Song) => void;
  pauseSong: () => void;
  nextSong: () => void;
  previousSong: () => void;
  setVolume: (volume: number) => void;
  seekTo: (time: number) => void;
  setPlayMode: (mode: AudioState['playMode']) => void;
}

export const useAudioStore = create<AudioState & AudioActions>((set, get) => ({
  // 状态初始值
  currentSong: null,
  isPlaying: false,
  volume: 50,
  currentTime: 0,
  duration: 0,
  playMode: 'sequence',
  queue: [],
  currentIndex: -1,
  
  // 动作方法
  playSong: (song) => set({ currentSong: song, isPlaying: true }),
  pauseSong: () => set({ isPlaying: false }),
  // ... 其他方法
}));
```

### 2. Tauri 命令封装

```typescript
// utils/tauri.ts
import { invoke } from '@tauri-apps/api/tauri';

export const musicAPI = {
  // 扫描音乐文件夹
  scanMusicFolder: (path: string): Promise<Song[]> =>
    invoke('scan_music_folder', { path }),
  
  // 获取音频元数据
  getAudioMetadata: (filePath: string): Promise<AudioMetadata> =>
    invoke('get_audio_metadata', { filePath }),
  
  // 数据库操作
  getAllSongs: (): Promise<Song[]> =>
    invoke('get_all_songs'),
  
  addSong: (song: Omit<Song, 'id'>): Promise<Song> =>
    invoke('add_song', { song }),
  
  createPlaylist: (name: string, description?: string): Promise<Playlist> =>
    invoke('create_playlist', { name, description }),
  
  addSongToPlaylist: (playlistId: number, songId: number): Promise<void> =>
    invoke('add_song_to_playlist', { playlistId, songId }),
};
```

### 3. 音频播放核心 Hook

```typescript
// hooks/useAudioPlayer.ts
export const useAudioPlayer = () => {
  const audioRef = useRef<HTMLAudioElement>(new Audio());
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    const audio = audioRef.current;
    
    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    
    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', handleSongEnd);
    
    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', handleSongEnd);
    };
  }, []);

  const play = useCallback(async (songPath?: string) => {
    const audio = audioRef.current;
    if (songPath) {
      audio.src = `asset://localhost/${songPath}`;
    }
    await audio.play();
    setIsPlaying(true);
  }, []);

  const pause = useCallback(() => {
    audioRef.current.pause();
    setIsPlaying(false);
  }, []);

  return {
    play,
    pause,
    isPlaying,
    currentTime,
    duration,
    seekTo: (time: number) => {
      audioRef.current.currentTime = time;
    },
    setVolume: (volume: number) => {
      audioRef.current.volume = volume / 100;
    }
  };
};
```

## 🚀 开发路线图

### Phase 1: 项目基础 (第1-2周)
- [x] Tauri 项目初始化
- [x] React + TypeScript + Vite 配置
- [x] shadcn/ui 安装和配置
- [x] Tailwind CSS 主题定制
- [x] 基础路由和布局组件
- [x] SQLite 数据库初始化

### Phase 2: 核心播放功能 (第3-4周)
- [x] 音频文件扫描和导入
- [x] 音频元数据解析 (ID3 标签)
- [x] 基础播放控制 (播放/暂停/上一首/下一首)
- [x] 音量控制和进度条
- [x] 播放队列管理
- [x] 播放模式 (顺序/随机/单曲循环)

### Phase 3: 音乐库管理 (第5-6周)
- [x] 歌曲列表展示 (Table 组件)
- [x] 专辑网格视图 (Card 组件)
- [x] 艺术家列表
- [x] 搜索和过滤功能
- [x] 排序功能 (按标题/艺术家/专辑/时长)
- [x] 专辑封面显示

### Phase 4: 播放列表功能 (第7周)
- [x] 创建/编辑/删除播放列表
- [x] 播放列表歌曲管理
- [x] 拖拽排序 (可选)
- [x] 智能播放列表 (基于条件)

### Phase 5: 高级功能 (第8-9周)
- [x] 音频可视化 (频谱/波形)
- [x] 快捷键支持
- [x] 播放历史记录
- [x] 歌词显示 (如果有 .lrc 文件)
- [x] 主题切换 (明暗模式)

### Phase 6: 优化和发布 (第10周)
- [x] 性能优化 (虚拟滚动等)
- [x] 错误处理和用户反馈
- [x] 自动更新机制
- [x] 打包配置优化
- [x] 用户文档编写

## 🎨 UI 设计重点

### 使用 shadcn/ui 的优势
1. **高度可定制**: 所有组件源码可见，易于修改
2. **类型安全**: 完整的 TypeScript 支持
3. **主题系统**: 基于 CSS 变量的主题切换
4. **无障碍访问**: 基于 Radix UI 的可访问性
5. **现代设计**: 符合当前设计趋势

### 关键 UI 组件定制
```typescript
// 自定义进度条样式
const progressVariants = cva(
  "relative h-2 w-full overflow-hidden rounded-full bg-secondary",
  {
    variants: {
      variant: {
        default: "bg-secondary",
        audio: "bg-muted cursor-pointer hover:bg-muted/80",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);
```

这个方案充分利用了 shadcn/ui 的优势，提供了完整的开发路线。需要我详细展开某个具体部分吗？
{"name": "tauri-music-player", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-sql": "^2.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "wavesurfer.js": "^7.10.1", "zustand": "^5.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tauri-apps/cli": "^2", "@types/node": "^24.1.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.6.2", "vite": "^6.0.3"}}
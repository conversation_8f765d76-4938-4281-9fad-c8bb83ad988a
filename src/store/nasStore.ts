import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { NASCredentials, NASConnectionState, NASFileInfo } from '@/types/nas';

interface NASState extends NASConnectionState {
  currentPath: string;
  files: NASFileInfo[];
  loading: boolean;
}

interface NASActions {
  setCredentials: (credentials: NASCredentials) => void;
  setConnected: (connected: boolean, sid?: string) => void;
  setError: (error: string | null) => void;
  setCurrentPath: (path: string) => void;
  setFiles: (files: NASFileInfo[]) => void;
  setLoading: (loading: boolean) => void;
  logout: () => void;
  clearError: () => void;
}

export const useNASStore = create<NASState & NASActions>()(
  persist(
    (set, get) => ({
      // State
      isConnected: false,
      credentials: null,
      sid: null,
      error: null,
      currentPath: '/music',
      files: [],
      loading: false,

      // Actions
      setCredentials: (credentials) => set({ credentials }),
      
      setConnected: (connected, sid) => set({ 
        isConnected: connected, 
        sid: sid || null,
        error: connected ? null : get().error
      }),
      
      setError: (error) => set({ error, isConnected: false }),
      
      setCurrentPath: (path) => set({ currentPath: path }),
      
      setFiles: (files) => set({ files }),
      
      setLoading: (loading) => set({ loading }),
      
      logout: () => set({ 
        isConnected: false, 
        sid: null, 
        credentials: null,
        error: null,
        files: [],
        currentPath: '/music'
      }),
      
      clearError: () => set({ error: null }),
    }),
    {
      name: 'nas-storage',
      partialize: (state) => ({
        credentials: state.credentials,
        isConnected: state.isConnected,
        sid: state.sid,
      }),
    }
  )
);

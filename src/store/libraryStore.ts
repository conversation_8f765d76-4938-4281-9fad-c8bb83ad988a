import { create } from 'zustand';
import { Song } from '@/types/audio';

interface LibraryState {
  songs: Song[];
  loading: boolean;
  error: string | null;
  searchQuery: string;
  sortBy: 'title' | 'artist' | 'album' | 'duration';
  sortOrder: 'asc' | 'desc';
}

interface LibraryActions {
  setSongs: (songs: Song[]) => void;
  addSong: (song: Song) => void;
  removeSong: (songId: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSearchQuery: (query: string) => void;
  setSortBy: (sortBy: LibraryState['sortBy']) => void;
  setSortOrder: (order: LibraryState['sortOrder']) => void;
  getFilteredSongs: () => Song[];
}

export const useLibraryStore = create<LibraryState & LibraryActions>((set, get) => ({
  // Initial state
  songs: [],
  loading: false,
  error: null,
  searchQuery: '',
  sortBy: 'title',
  sortOrder: 'asc',

  // Actions
  setSongs: (songs) => set({ songs }),

  addSong: (song) => {
    const { songs } = get();
    set({ songs: [...songs, song] });
  },

  removeSong: (songId) => {
    const { songs } = get();
    set({ songs: songs.filter(song => song.id !== songId) });
  },

  setLoading: (loading) => set({ loading }),

  setError: (error) => set({ error }),

  setSearchQuery: (query) => set({ searchQuery: query }),

  setSortBy: (sortBy) => set({ sortBy }),

  setSortOrder: (order) => set({ sortOrder: order }),

  getFilteredSongs: () => {
    const { songs, searchQuery, sortBy, sortOrder } = get();
    
    // Filter by search query
    let filteredSongs = songs;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredSongs = songs.filter(song => 
        song.title.toLowerCase().includes(query) ||
        song.artist?.toLowerCase().includes(query) ||
        song.album?.toLowerCase().includes(query)
      );
    }

    // Sort songs
    filteredSongs.sort((a, b) => {
      let aValue: string | number = '';
      let bValue: string | number = '';

      switch (sortBy) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'artist':
          aValue = (a.artist || '').toLowerCase();
          bValue = (b.artist || '').toLowerCase();
          break;
        case 'album':
          aValue = (a.album || '').toLowerCase();
          bValue = (b.album || '').toLowerCase();
          break;
        case 'duration':
          aValue = a.duration;
          bValue = b.duration;
          break;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filteredSongs;
  },
}));

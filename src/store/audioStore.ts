import { create } from 'zustand';
import { Song, PlayMode } from '@/types/audio';

interface AudioState {
  currentSong: Song | null;
  isPlaying: boolean;
  volume: number;
  currentTime: number;
  duration: number;
  playMode: PlayMode;
  queue: Song[];
  currentIndex: number;
}

interface AudioActions {
  playSong: (song: Song) => void;
  pauseSong: () => void;
  resumeSong: () => void;
  nextSong: () => void;
  previousSong: () => void;
  setVolume: (volume: number) => void;
  seekTo: (time: number) => void;
  setPlayMode: (mode: PlayMode) => void;
  setQueue: (songs: Song[]) => void;
  setCurrentTime: (time: number) => void;
  setDuration: (duration: number) => void;
  setCurrentIndex: (index: number) => void;
}

export const useAudioStore = create<AudioState & AudioActions>((set, get) => ({
  // Initial state
  currentSong: null,
  isPlaying: false,
  volume: 50,
  currentTime: 0,
  duration: 0,
  playMode: 'sequence',
  queue: [],
  currentIndex: -1,

  // Actions
  playSong: (song) => {
    const { queue } = get();
    const index = queue.findIndex(s => s.id === song.id);
    set({ 
      currentSong: song, 
      isPlaying: true,
      currentIndex: index >= 0 ? index : 0
    });
  },

  pauseSong: () => set({ isPlaying: false }),

  resumeSong: () => set({ isPlaying: true }),

  nextSong: () => {
    const { queue, currentIndex, playMode } = get();
    if (queue.length === 0) return;

    let nextIndex = currentIndex + 1;
    
    if (playMode === 'shuffle') {
      nextIndex = Math.floor(Math.random() * queue.length);
    } else if (playMode === 'repeat') {
      if (nextIndex >= queue.length) {
        nextIndex = 0;
      }
    } else { // sequence
      if (nextIndex >= queue.length) {
        set({ isPlaying: false });
        return;
      }
    }

    const nextSong = queue[nextIndex];
    if (nextSong) {
      set({
        currentSong: nextSong,
        currentIndex: nextIndex,
        isPlaying: true
      });
    }
  },

  previousSong: () => {
    const { queue, currentIndex } = get();
    if (queue.length === 0) return;

    let prevIndex = currentIndex - 1;
    if (prevIndex < 0) {
      prevIndex = queue.length - 1;
    }

    const prevSong = queue[prevIndex];
    if (prevSong) {
      set({
        currentSong: prevSong,
        currentIndex: prevIndex,
        isPlaying: true
      });
    }
  },

  setVolume: (volume) => set({ volume }),

  seekTo: (time) => set({ currentTime: time }),

  setPlayMode: (mode) => set({ playMode: mode }),

  setQueue: (songs) => set({ queue: songs }),

  setCurrentTime: (time) => set({ currentTime: time }),

  setDuration: (duration) => set({ duration }),

  setCurrentIndex: (index) => set({ currentIndex: index }),
}));

import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useNASStore } from '@/store/nasStore'
import { useAudioStore } from '@/store/audioStore'
import { NASApi } from '@/utils/nasApi'
import { NASFileInfo } from '@/types/nas'
import { Song } from '@/types/audio'
import { Folder, Music, ArrowLeft, RefreshCw, Play, Loader2, LogOut } from 'lucide-react'

const AUDIO_EXTENSIONS = ['mp3', 'flac', 'aac', 'ogg', 'wav', 'm4a', 'wma']

export const FileBrowser: React.FC = () => {
  const { credentials, sid, currentPath, files, loading, setCurrentPath, setFiles, setLoading, setError, logout } =
    useNASStore()

  const { playSong, setQueue } = useAudioStore()
  const [nasApi, setNasApi] = useState<NASApi | null>(null)

  useEffect(() => {
    if (credentials && sid) {
      const api = new NASApi(credentials)
      api.setSid(sid)
      setNasApi(api)
      loadFiles(currentPath)
    }
  }, [credentials, sid, currentPath])

  const loadFiles = async (path: string) => {
    if (!nasApi) return

    setLoading(true)
    try {
      const result = await nasApi.listFiles(path)
      if (result.success && result.data) {
        setFiles(result.data.files)
        setCurrentPath(path)
      } else {
        setError(result.error || 'Failed to load files')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const navigateToFolder = (folderPath: string) => {
    loadFiles(folderPath)
  }

  const navigateUp = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/'
    loadFiles(parentPath)
  }

  const isAudioFile = (filename: string): boolean => {
    const extension = filename.split('.').pop()?.toLowerCase()
    return extension ? AUDIO_EXTENSIONS.includes(extension) : false
  }

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return ''
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const playAudioFile = async (file: NASFileInfo) => {
    if (!isAudioFile(file.name)) return

    try {
      // Check if we're in test mode (mock session)
      const isTestMode = sid === 'mock-session-id'

      let fileUrl: string
      if (isTestMode) {
        // In test mode, use a placeholder URL
        fileUrl = `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav` // Demo audio file
      } else if (nasApi) {
        const url = await nasApi.getFileUrl(file.path)
        if (!url) return
        fileUrl = url
      } else {
        return
      }

      const song: Song = {
        id: Date.now(), // Temporary ID
        title: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
        artist: 'Unknown Artist',
        album: 'Unknown Album',
        duration: 0, // Will be determined when loaded
        file_path: fileUrl,
        date_added: new Date().toISOString(),
        play_count: 0
      }

      // Get all audio files in current directory for queue
      const audioFiles = files.filter((f) => !f.isdir && isAudioFile(f.name))

      let queue: Song[]
      if (isTestMode) {
        queue = audioFiles.map((f, index) => ({
          id: Date.now() + index,
          title: f.name.replace(/\.[^/.]+$/, ''),
          artist: 'Unknown Artist',
          album: 'Unknown Album',
          duration: 0,
          file_path: `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`,
          date_added: new Date().toISOString(),
          play_count: 0
        }))
      } else if (nasApi) {
        queue = await Promise.all(
          audioFiles.map(async (f, index) => ({
            id: Date.now() + index,
            title: f.name.replace(/\.[^/.]+$/, ''),
            artist: 'Unknown Artist',
            album: 'Unknown Album',
            duration: 0,
            file_path: (await nasApi.getFileUrl(f.path)) || '',
            date_added: new Date().toISOString(),
            play_count: 0
          }))
        )
      } else {
        queue = []
      }

      setQueue(queue)
      playSong(song)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to play file')
    }
  }

  const handleLogout = async () => {
    if (nasApi) {
      await nasApi.logout()
    }
    logout()
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Music className="h-5 w-5" />
              NAS Music Browser
            </CardTitle>
            <Button variant="outline" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Navigation */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 mb-4">
            <Button variant="outline" size="sm" onClick={navigateUp} disabled={currentPath === '/' || loading}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Up
            </Button>
            <Button variant="outline" size="sm" onClick={() => loadFiles(currentPath)} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <span className="text-sm text-muted-foreground">Current: {currentPath}</span>
          </div>

          {/* File List */}
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading files...
            </div>
          ) : (
            <div className="space-y-2">
              {files.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer"
                  onClick={() => {
                    if (file.isdir) {
                      navigateToFolder(file.path)
                    } else if (isAudioFile(file.name)) {
                      playAudioFile(file)
                    }
                  }}
                >
                  <div className="flex-shrink-0">
                    {file.isdir ? (
                      <Folder className="h-5 w-5 text-blue-500" />
                    ) : isAudioFile(file.name) ? (
                      <Music className="h-5 w-5 text-green-500" />
                    ) : (
                      <div className="h-5 w-5 bg-gray-300 rounded" />
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{file.name}</div>
                    {!file.isdir && <div className="text-sm text-muted-foreground">{formatFileSize(file.size)}</div>}
                  </div>

                  {!file.isdir && isAudioFile(file.name) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        playAudioFile(file)
                      }}
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}

              {files.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">No files found in this directory</div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

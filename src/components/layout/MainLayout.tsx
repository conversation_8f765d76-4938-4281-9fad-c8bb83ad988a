import React from 'react';
import { AudioPlayer } from '@/components/audio/AudioPlayer';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-4 space-y-4">
        {/* Main Content */}
        <main className="flex-1">
          {children}
        </main>

        {/* Audio Player - Fixed at bottom */}
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-background/95 backdrop-blur-sm border-t">
          <div className="container mx-auto">
            <AudioPlayer />
          </div>
        </div>

        {/* Spacer to prevent content from being hidden behind fixed player */}
        <div className="h-32" />
      </div>
    </div>
  );
};

import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useLibraryStore } from '@/store/libraryStore';
import { useAudioStore } from '@/store/audioStore';
import { Play, Pause } from 'lucide-react';
import { Song } from '@/types/audio';

const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

export const SongList: React.FC = () => {
  const { getFilteredSongs } = useLibraryStore();
  const { 
    currentSong, 
    isPlaying, 
    playSong, 
    pauseSong, 
    resumeSong,
    setQueue 
  } = useAudioStore();

  const songs = getFilteredSongs();

  const handleSongClick = (song: Song) => {
    if (currentSong?.id === song.id) {
      if (isPlaying) {
        pauseSong();
      } else {
        resumeSong();
      }
    } else {
      setQueue(songs);
      playSong(song);
    }
  };

  const isCurrentSong = (song: Song) => currentSong?.id === song.id;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Songs ({songs.length})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          {songs.map((song) => (
            <div
              key={song.id}
              className={`flex items-center gap-3 p-2 rounded-md hover:bg-muted/50 cursor-pointer ${
                isCurrentSong(song) ? 'bg-muted' : ''
              }`}
              onClick={() => handleSongClick(song)}
            >
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={(e) => {
                  e.stopPropagation();
                  handleSongClick(song);
                }}
              >
                {isCurrentSong(song) && isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>

              <div className="flex-1 min-w-0 grid grid-cols-3 gap-4">
                <div className="truncate">
                  <span className={`font-medium ${isCurrentSong(song) ? 'text-primary' : ''}`}>
                    {song.title}
                  </span>
                </div>
                <div className="truncate text-sm text-muted-foreground">
                  {song.artist || 'Unknown Artist'}
                </div>
                <div className="text-sm text-muted-foreground text-right">
                  {formatDuration(song.duration)}
                </div>
              </div>
            </div>
          ))}

          {songs.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No songs found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

import React from 'react';
import { Slider } from '@/components/ui/slider';
import { useAudioStore } from '@/store/audioStore';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';

const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

export const ProgressBar: React.FC = () => {
  const { currentTime, duration } = useAudioStore();
  const { seekTo } = useAudioPlayer();

  const handleSeek = (values: number[]) => {
    seekTo(values[0]);
  };

  return (
    <div className="flex items-center gap-3 w-full">
      <span className="text-sm text-muted-foreground min-w-[40px]">
        {formatTime(currentTime)}
      </span>
      
      <Slider
        value={[currentTime]}
        max={duration || 100}
        step={1}
        onValueChange={handleSeek}
        className="flex-1"
      />
      
      <span className="text-sm text-muted-foreground min-w-[40px]">
        {formatTime(duration)}
      </span>
    </div>
  );
};

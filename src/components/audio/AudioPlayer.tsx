import React from 'react';
import { Card } from '@/components/ui/card';
import { useAudioStore } from '@/store/audioStore';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { PlaybackControls } from './PlaybackControls';
import { VolumeControl } from './VolumeControl';
import { ProgressBar } from './ProgressBar';

export const AudioPlayer: React.FC = () => {
  const { currentSong } = useAudioStore();
  
  // Initialize audio player
  useAudioPlayer();

  if (!currentSong) {
    return (
      <Card className="p-4">
        <div className="text-center text-muted-foreground">
          No song selected
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4">
      <div className="space-y-4">
        {/* Song Info */}
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
            <span className="text-xs">♪</span>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium truncate">{currentSong.title}</h3>
            <p className="text-sm text-muted-foreground truncate">
              {currentSong.artist || 'Unknown Artist'}
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <ProgressBar />

        {/* Controls */}
        <div className="flex items-center justify-between">
          <PlaybackControls />
          <VolumeControl />
        </div>
      </div>
    </Card>
  );
};

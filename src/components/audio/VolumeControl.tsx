import React from 'react';
import { Slider } from '@/components/ui/slider';
import { useAudioStore } from '@/store/audioStore';
import { Volume2, VolumeX } from 'lucide-react';
import { Button } from '@/components/ui/button';

export const VolumeControl: React.FC = () => {
  const { volume, setVolume } = useAudioStore();

  const handleVolumeChange = (values: number[]) => {
    setVolume(values[0]);
  };

  const toggleMute = () => {
    setVolume(volume > 0 ? 0 : 50);
  };

  return (
    <div className="flex items-center gap-2 w-32">
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleMute}
        className="h-8 w-8"
      >
        {volume > 0 ? (
          <Volume2 className="h-4 w-4" />
        ) : (
          <VolumeX className="h-4 w-4" />
        )}
      </Button>
      
      <Slider
        value={[volume]}
        max={100}
        step={1}
        onValueChange={handleVolumeChange}
        className="flex-1"
      />
    </div>
  );
};

import React from 'react';
import { Button } from '@/components/ui/button';
import { useAudioStore } from '@/store/audioStore';
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward, 
  Repeat, 
  Shuffle,
  RotateCcw
} from 'lucide-react';

export const PlaybackControls: React.FC = () => {
  const {
    isPlaying,
    playMode,
    currentSong,
    playSong,
    pauseSong,
    resumeSong,
    nextSong,
    previousSong,
    setPlayMode,
  } = useAudioStore();

  const handlePlayPause = () => {
    if (isPlaying) {
      pauseSong();
    } else {
      if (currentSong) {
        resumeSong();
      }
    }
  };

  const handlePlayModeToggle = () => {
    const modes: Array<typeof playMode> = ['sequence', 'repeat', 'shuffle'];
    const currentIndex = modes.indexOf(playMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setPlayMode(modes[nextIndex]);
  };

  const getPlayModeIcon = () => {
    switch (playMode) {
      case 'repeat':
        return <Repeat className="h-4 w-4" />;
      case 'shuffle':
        return <Shuffle className="h-4 w-4" />;
      default:
        return <RotateCcw className="h-4 w-4" />;
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="ghost"
        size="icon"
        onClick={handlePlayModeToggle}
        className={playMode !== 'sequence' ? 'text-primary' : ''}
      >
        {getPlayModeIcon()}
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={previousSong}
        disabled={!currentSong}
      >
        <SkipBack className="h-4 w-4" />
      </Button>

      <Button
        variant="default"
        size="icon"
        onClick={handlePlayPause}
        disabled={!currentSong}
        className="h-10 w-10"
      >
        {isPlaying ? (
          <Pause className="h-5 w-5" />
        ) : (
          <Play className="h-5 w-5" />
        )}
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={nextSong}
        disabled={!currentSong}
      >
        <SkipForward className="h-4 w-4" />
      </Button>
    </div>
  );
};

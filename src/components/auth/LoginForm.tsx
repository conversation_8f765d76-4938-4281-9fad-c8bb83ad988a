import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useNASStore } from '@/store/nasStore'
import { NASApi } from '@/utils/nasApi'
import { NASCredentials } from '@/types/nas'
import { TestModeButton } from './TestModeButton'
import { ConnectionTester } from './ConnectionTester'
import { TroubleshootingGuide } from './TroubleshootingGuide'
import { Loader2, Server, Lock, User, Globe, Settings } from 'lucide-react'

export const LoginForm: React.FC = () => {
  const [formData, setFormData] = useState<NASCredentials>({
    host: '*************',
    port: 27323,
    username: '',
    password: '',
    useHttps: false
  })
  const [loading, setLoading] = useState(false)
  const [showTester, setShowTester] = useState(false)
  const [showTroubleshooting, setShowTroubleshooting] = useState(false)

  const { setCredentials, setConnected, setError, error, clearError } = useNASStore()

  const handleInputChange = (field: keyof NASCredentials, value: string | number | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    clearError()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    clearError()

    try {
      const nasApi = new NASApi(formData)
      const result = await nasApi.login(formData)

      if (result.success && result.sid) {
        setCredentials(formData)
        setConnected(true, result.sid)
      } else {
        setError(result.error || 'Login failed')
        setShowTroubleshooting(true)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection failed')
      setShowTroubleshooting(true)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-4xl space-y-6">
        {showTester && <ConnectionTester />}
        {showTroubleshooting && (
          <TroubleshootingGuide host={formData.host} port={formData.port} useHttps={formData.useHttps} />
        )}

        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <Server className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-2xl">Connect to Synology NAS</CardTitle>
            <CardDescription>Enter your NAS credentials to access your music library</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="host">NAS Host/IP</Label>
                <div className="relative">
                  <Globe className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="host"
                    type="text"
                    placeholder="************* or nas.example.com"
                    value={formData.host}
                    onChange={(e) => handleInputChange('host', e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="port">Port</Label>
                  <Input
                    id="port"
                    type="number"
                    placeholder="5000"
                    value={formData.port}
                    onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 5000)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="https">Protocol</Label>
                  <select
                    id="https"
                    value={formData.useHttps ? 'https' : 'http'}
                    onChange={(e) => handleInputChange('useHttps', e.target.value === 'https')}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="http">HTTP</option>
                    <option value="https">HTTPS</option>
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="username"
                    type="text"
                    placeholder="Your NAS username"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="Your NAS password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              {error && <div className="p-3 text-sm text-destructive bg-destructive/10 rounded-md">{error}</div>}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  'Connect to NAS'
                )}
              </Button>
            </form>

            <div className="space-y-2">
              <Button type="button" variant="outline" onClick={() => setShowTester(!showTester)} className="w-full">
                <Settings className="mr-2 h-4 w-4" />
                {showTester ? 'Hide' : 'Show'} Connection Tester
              </Button>

              <TestModeButton />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

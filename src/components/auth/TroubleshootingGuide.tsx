import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';

interface TroubleshootingGuideProps {
  host: string;
  port: number;
  useHttps: boolean;
}

export const TroubleshootingGuide: React.FC<TroubleshootingGuideProps> = ({ 
  host, 
  port, 
  useHttps 
}) => {
  const protocol = useHttps ? 'https' : 'http';
  const dsmUrl = `${protocol}://${host}:${port}`;

  const troubleshootingSteps = [
    {
      title: '1. 验证NAS网络连接',
      items: [
        `确认NAS IP地址正确: ${host}`,
        `确认端口号正确: ${port}`,
        '确认NAS已开机并连接到网络',
        '尝试ping NAS IP地址测试网络连通性'
      ]
    },
    {
      title: '2. 检查DSM访问',
      items: [
        `在浏览器中访问: ${dsmUrl}`,
        '确认能够看到DSM登录页面',
        '如果无法访问，检查端口设置',
        '常用端口: 5000 (HTTP), 5001 (HTTPS)'
      ]
    },
    {
      title: '3. 检查防火墙设置',
      items: [
        '确认NAS防火墙允许相应端口访问',
        '检查路由器防火墙设置',
        '确认没有其他网络设备阻止连接'
      ]
    },
    {
      title: '4. 验证WebAPI服务',
      items: [
        '确认Web Station服务已启用',
        '检查DSM > 控制面板 > 终端机和SNMP',
        '确认启用SSH服务（如果需要）',
        '检查用户权限设置'
      ]
    },
    {
      title: '5. 常见端口说明',
      items: [
        '5000: DSM HTTP默认端口',
        '5001: DSM HTTPS默认端口', 
        '27323: 可能是自定义端口',
        '检查DSM > 控制面板 > 网络 > DSM设置'
      ]
    }
  ];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-orange-500" />
          连接故障排除指南
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <ExternalLink className="h-4 w-4" />
            <span className="font-medium">快速测试</span>
          </div>
          <p className="text-sm">
            首先尝试在浏览器中访问: 
            <a 
              href={dsmUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="ml-1 text-blue-600 hover:underline"
            >
              {dsmUrl}
            </a>
          </p>
        </div>

        {troubleshootingSteps.map((step, index) => (
          <div key={index} className="space-y-2">
            <h3 className="font-medium text-lg">{step.title}</h3>
            <ul className="space-y-1">
              {step.items.map((item, itemIndex) => (
                <li key={itemIndex} className="flex items-start gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>{item}</span>
                </li>
              ))}
            </ul>
          </div>
        ))}

        <div className="p-4 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
          <h3 className="font-medium mb-2">CORS 问题说明</h3>
          <p className="text-sm text-muted-foreground">
            由于浏览器的CORS（跨域资源共享）限制，直接从网页访问NAS可能会遇到问题。
            这是正常的安全限制。建议使用Tauri桌面应用来避免这些限制。
          </p>
        </div>

        <div className="p-4 bg-green-50 dark:bg-green-950 rounded-lg">
          <h3 className="font-medium mb-2">推荐设置</h3>
          <ul className="text-sm space-y-1">
            <li>• 使用标准端口: 5000 (HTTP) 或 5001 (HTTPS)</li>
            <li>• 确保启用了Web Station服务</li>
            <li>• 为音乐播放器创建专用用户账户</li>
            <li>• 给用户分配适当的文件访问权限</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

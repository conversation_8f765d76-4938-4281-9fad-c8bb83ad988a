import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { NASCredentials } from '@/types/nas';
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface TestResult {
  step: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message: string;
}

export const ConnectionTester: React.FC = () => {
  const [credentials, setCredentials] = useState<NASCredentials>({
    host: '*************',
    port: 27323,
    username: '',
    password: '',
    useHttps: false,
  });
  
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const addResult = (step: string, status: TestResult['status'], message: string) => {
    setResults(prev => [...prev, { step, status, message }]);
  };

  const testConnection = async () => {
    setTesting(true);
    setResults([]);

    const protocol = credentials.useHttps ? 'https' : 'http';
    const baseUrl = `${protocol}://${credentials.host}:${credentials.port}`;

    // Test 1: Basic connectivity
    addResult('Basic Connectivity', 'pending', 'Testing basic network connectivity...');
    
    try {
      // Try a simple ping-like test
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(`${baseUrl}/`, {
        method: 'HEAD',
        signal: controller.signal,
        mode: 'no-cors' // This will help bypass CORS for basic connectivity test
      });
      
      clearTimeout(timeoutId);
      addResult('Basic Connectivity', 'success', `Successfully reached ${baseUrl}`);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        addResult('Basic Connectivity', 'error', 'Connection timeout - NAS may be unreachable');
      } else {
        addResult('Basic Connectivity', 'warning', `Network request failed, but this might be due to CORS. Error: ${error instanceof Error ? error.message : 'Unknown'}`);
      }
    }

    // Test 2: DSM Web Interface
    addResult('DSM Web Interface', 'pending', 'Testing DSM web interface...');
    
    try {
      const dsmResponse = await fetch(`${baseUrl}/webman/index.cgi`, {
        method: 'HEAD',
        mode: 'no-cors'
      });
      addResult('DSM Web Interface', 'success', 'DSM web interface appears to be running');
    } catch (error) {
      addResult('DSM Web Interface', 'warning', 'Could not verify DSM web interface (may be CORS-related)');
    }

    // Test 3: WebAPI availability
    addResult('WebAPI Availability', 'pending', 'Testing WebAPI availability...');
    
    try {
      const apiResponse = await fetch(`${baseUrl}/webapi/query.cgi?api=SYNO.API.Info&version=1&method=query&query=SYNO.API.Auth`, {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (apiResponse.ok) {
        const data = await apiResponse.json();
        if (data.success) {
          addResult('WebAPI Availability', 'success', 'WebAPI is accessible and responding correctly');
        } else {
          addResult('WebAPI Availability', 'error', 'WebAPI responded but returned an error');
        }
      } else {
        addResult('WebAPI Availability', 'error', `WebAPI returned HTTP ${apiResponse.status}`);
      }
    } catch (error) {
      addResult('WebAPI Availability', 'error', `WebAPI test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Test 4: Authentication (if credentials provided)
    if (credentials.username && credentials.password) {
      addResult('Authentication', 'pending', 'Testing authentication...');
      
      try {
        const authResponse = await fetch(`${baseUrl}/webapi/auth.cgi`, {
          method: 'POST',
          mode: 'cors',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
          },
          body: new URLSearchParams({
            api: 'SYNO.API.Auth',
            version: '3',
            method: 'login',
            account: credentials.username,
            passwd: credentials.password,
            session: 'FileStation',
            format: 'sid',
          }),
        });

        if (authResponse.ok) {
          const authData = await authResponse.json();
          if (authData.success) {
            addResult('Authentication', 'success', 'Authentication successful!');
          } else {
            addResult('Authentication', 'error', `Authentication failed: ${authData.error?.code || 'Unknown error'}`);
          }
        } else {
          addResult('Authentication', 'error', `Authentication request failed: HTTP ${authResponse.status}`);
        }
      } catch (error) {
        addResult('Authentication', 'error', `Authentication test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    setTesting(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>NAS Connection Tester</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="test-host">Host/IP</Label>
            <Input
              id="test-host"
              value={credentials.host}
              onChange={(e) => setCredentials(prev => ({ ...prev, host: e.target.value }))}
              placeholder="*************"
            />
          </div>
          <div>
            <Label htmlFor="test-port">Port</Label>
            <Input
              id="test-port"
              type="number"
              value={credentials.port}
              onChange={(e) => setCredentials(prev => ({ ...prev, port: parseInt(e.target.value) || 5000 }))}
              placeholder="27323"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="test-username">Username (optional)</Label>
            <Input
              id="test-username"
              value={credentials.username}
              onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
              placeholder="admin"
            />
          </div>
          <div>
            <Label htmlFor="test-password">Password (optional)</Label>
            <Input
              id="test-password"
              type="password"
              value={credentials.password}
              onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
              placeholder="password"
            />
          </div>
        </div>

        <div>
          <Label>Protocol</Label>
          <select
            value={credentials.useHttps ? 'https' : 'http'}
            onChange={(e) => setCredentials(prev => ({ ...prev, useHttps: e.target.value === 'https' }))}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
          >
            <option value="http">HTTP</option>
            <option value="https">HTTPS</option>
          </select>
        </div>

        <Button onClick={testConnection} disabled={testing} className="w-full">
          {testing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Testing Connection...
            </>
          ) : (
            'Test Connection'
          )}
        </Button>

        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium">Test Results:</h3>
            {results.map((result, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-md">
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <div className="font-medium">{result.step}</div>
                  <div className="text-sm text-muted-foreground">{result.message}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, CheckCircle, XCircle, Info, ExternalLink } from 'lucide-react';

export const NetworkDiagnostic: React.FC<{ host: string; port: number }> = ({ host, port }) => {
  const [diagnosticResults, setDiagnosticResults] = useState<string[]>([]);

  const runDiagnostic = () => {
    const results = [];
    
    // 分析端口27323
    if (port === 27323) {
      results.push('🔍 检测到端口27323 - 这不是群晖DSM的标准端口');
      results.push('💡 标准端口: 5000(HTTP) 或 5001(HTTPS)');
      results.push('⚠️  端口27323可能是：');
      results.push('   • 自定义DSM端口');
      results.push('   • 路由器端口转发');
      results.push('   • 其他服务端口');
    }

    // 分析IP地址
    if (host.startsWith('192.168.31.')) {
      results.push('🌐 检测到小米路由器网段 (192.168.31.x)');
      results.push('📡 建议检查路由器管理界面中的设备列表');
    }

    // 提供具体建议
    results.push('');
    results.push('🔧 立即尝试的解决方案：');
    results.push('1. 在浏览器中测试这些地址：');
    results.push(`   • http://${host}:5000`);
    results.push(`   • https://${host}:5001`);
    results.push(`   • http://${host}:${port}`);
    results.push('');
    results.push('2. 检查群晖DSM设置：');
    results.push('   • 控制面板 > 网络 > DSM设置');
    results.push('   • 查看HTTP/HTTPS端口配置');
    results.push('');
    results.push('3. 检查网络连接：');
    results.push(`   • ping ${host}`);
    results.push('   • 确认NAS在同一网络');

    setDiagnosticResults(results);
  };

  const testUrls = [
    { url: `http://${host}:5000`, name: 'DSM HTTP (标准)' },
    { url: `https://${host}:5001`, name: 'DSM HTTPS (标准)' },
    { url: `http://${host}:${port}`, name: `HTTP :${port}` },
    { url: `https://${host}:${port}`, name: `HTTPS :${port}` },
  ];

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          网络诊断 - {host}:{port}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {testUrls.map((test, index) => (
            <Button
              key={index}
              variant="outline"
              className="justify-between h-auto p-3"
              onClick={() => window.open(test.url, '_blank')}
            >
              <div className="text-left">
                <div className="font-medium text-sm">{test.name}</div>
                <div className="text-xs text-muted-foreground">{test.url}</div>
              </div>
              <ExternalLink className="h-4 w-4" />
            </Button>
          ))}
        </div>

        <Button onClick={runDiagnostic} className="w-full">
          运行网络诊断
        </Button>

        {diagnosticResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium">诊断结果：</h3>
            <div className="bg-muted p-4 rounded-lg text-sm space-y-1 font-mono">
              {diagnosticResults.map((result, index) => (
                <div key={index} className={result.trim() === '' ? 'h-2' : ''}>
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-blue-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium mb-1">关于"Load failed"错误：</p>
              <p>这通常表示：</p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>网络连接被阻止或超时</li>
                <li>端口不正确或服务未运行</li>
                <li>防火墙阻止了连接</li>
                <li>HTTPS证书问题（如果使用HTTPS）</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium mb-1">快速解决方案：</p>
              <ol className="list-decimal list-inside space-y-1">
                <li>点击上方按钮测试不同端口</li>
                <li>如果5000或5001端口能打开，请使用那个端口</li>
                <li>确认NAS电源开启且网络连接正常</li>
                <li>检查路由器是否有端口转发设置</li>
              </ol>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

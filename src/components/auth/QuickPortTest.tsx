import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, Loader2, ExternalLink } from 'lucide-react';

interface PortTestResult {
  url: string;
  protocol: string;
  port: number;
  status: 'testing' | 'success' | 'failed';
  message: string;
}

export const QuickPortTest: React.FC<{ host: string }> = ({ host }) => {
  const [results, setResults] = useState<PortTestResult[]>([]);
  const [testing, setTesting] = useState(false);

  const commonPorts = [
    { protocol: 'http', port: 5000, name: 'DSM HTTP (默认)' },
    { protocol: 'https', port: 5001, name: 'DSM HTTPS (默认)' },
    { protocol: 'http', port: 27323, name: '当前端口 HTTP' },
    { protocol: 'https', port: 27323, name: '当前端口 HTTPS' },
  ];

  const testPorts = async () => {
    setTesting(true);
    setResults([]);

    for (const portConfig of commonPorts) {
      const url = `${portConfig.protocol}://${host}:${portConfig.port}`;
      
      setResults(prev => [...prev, {
        url,
        protocol: portConfig.protocol,
        port: portConfig.port,
        status: 'testing',
        message: `测试 ${portConfig.name}...`
      }]);

      try {
        // 使用fetch测试连接，设置较短的超时时间
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);

        const response = await fetch(url, {
          method: 'HEAD',
          signal: controller.signal,
          mode: 'no-cors' // 避免CORS问题
        });

        clearTimeout(timeoutId);
        
        setResults(prev => prev.map(result => 
          result.url === url 
            ? { ...result, status: 'success', message: `✅ ${portConfig.name} - 可访问` }
            : result
        ));
      } catch (error) {
        setResults(prev => prev.map(result => 
          result.url === url 
            ? { 
                ...result, 
                status: 'failed', 
                message: `❌ ${portConfig.name} - ${error instanceof Error && error.name === 'AbortError' ? '连接超时' : '连接失败'}`
              }
            : result
        ));
      }

      // 添加小延迟避免过快请求
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setTesting(false);
  };

  const getStatusIcon = (status: PortTestResult['status']) => {
    switch (status) {
      case 'testing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-lg">快速端口测试</CardTitle>
        <p className="text-sm text-muted-foreground">
          测试常用端口以找到正确的DSM访问地址
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={testPorts} 
          disabled={testing}
          className="w-full"
        >
          {testing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              测试中...
            </>
          ) : (
            '开始端口测试'
          )}
        </Button>

        {results.length > 0 && (
          <div className="space-y-2">
            {results.map((result, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded-md">
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <div className="font-medium text-sm">{result.message}</div>
                  <div className="text-xs text-muted-foreground">{result.url}</div>
                </div>
                {result.status === 'success' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(result.url, '_blank')}
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}

        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>说明：</strong></p>
          <p>• 5000/5001 是群晖DSM的标准端口</p>
          <p>• 27323 可能是自定义端口</p>
          <p>• 如果测试成功，点击外链图标在浏览器中打开</p>
        </div>
      </CardContent>
    </Card>
  );
};

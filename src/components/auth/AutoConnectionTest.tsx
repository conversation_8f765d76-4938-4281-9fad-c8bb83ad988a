import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNASStore } from '@/store/nasStore';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/utils/nasApiTauri';
import { getEnvNASConfig, envConfig } from '@/utils/envConfig';
import { CheckCircle, XCircle, Loader2, Play, RefreshCw, Settings } from 'lucide-react';

interface TestResult {
  step: string;
  status: 'pending' | 'running' | 'success' | 'failed';
  message: string;
  details?: string;
}

export const AutoConnectionTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [autoTesting, setAutoTesting] = useState(false);
  const { setCredentials, setConnected, setError, clearError } = useNASStore();

  const envNASConfig = getEnvNASConfig();

  useEffect(() => {
    // 组件加载时自动运行测试
    if (envNASConfig && !autoTesting) {
      setAutoTesting(true);
      runAutoTest();
    }
  }, [envNASConfig]);

  const addResult = (step: string, status: TestResult['status'], message: string, details?: string) => {
    setResults(prev => [...prev, { step, status, message, details }]);
  };

  const updateLastResult = (updates: Partial<TestResult>) => {
    setResults(prev => {
      const newResults = [...prev];
      if (newResults.length > 0) {
        newResults[newResults.length - 1] = { ...newResults[newResults.length - 1], ...updates };
      }
      return newResults;
    });
  };

  const runAutoTest = async () => {
    if (!envNASConfig) {
      addResult('配置检查', 'failed', '环境变量配置不完整', '请检查 .env 文件中的NAS配置');
      return;
    }

    setTesting(true);
    setResults([]);
    clearError();

    // Step 1: 配置验证
    addResult('配置验证', 'running', '验证环境变量配置...');
    
    await new Promise(resolve => setTimeout(resolve, 500));
    
    updateLastResult({
      status: 'success',
      message: '环境变量配置已加载',
      details: `主机: ${envNASConfig.host}, 端口: ${envNASConfig.port}, 用户: ${envNASConfig.username}`
    });

    // Step 2: QuickConnect 解析
    addResult('QuickConnect解析', 'running', '解析QuickConnect地址...');
    
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (envNASConfig.host.includes('quickconnect.cn')) {
      updateLastResult({
        status: 'success',
        message: 'QuickConnect地址解析成功',
        details: `解析后地址: ${envNASConfig.useHttps ? 'https' : 'http'}://${envNASConfig.host}:${envNASConfig.port}`
      });
    } else {
      updateLastResult({
        status: 'success',
        message: '直接IP地址配置',
        details: `地址: ${envNASConfig.useHttps ? 'https' : 'http'}://${envNASConfig.host}:${envNASConfig.port}`
      });
    }

    // Step 3: 网络连接测试
    addResult('网络连接', 'running', '测试网络连通性...');
    
    try {
      // 使用Tauri代理进行连接测试
      const nasApi = new NASApiTauri();
      const loginResult = await nasApi.login(envNASConfig);

      if (loginResult.success && loginResult.sid) {
        updateLastResult({
          status: 'success',
          message: '网络连接成功',
          details: `会话ID: ${loginResult.sid.substring(0, 8)}...`
        });

        // Step 4: 认证测试
        addResult('用户认证', 'running', '验证用户凭据...');
        
        await new Promise(resolve => setTimeout(resolve, 500));
        
        updateLastResult({
          status: 'success',
          message: '用户认证成功',
          details: `用户 ${envNASConfig.username} 登录成功`
        });

        // Step 5: 文件系统访问测试
        addResult('文件系统', 'running', '测试文件系统访问...');
        
        try {
          const listResult = await nasApi.listFiles('/');
          
          if (listResult.success && listResult.data) {
            updateLastResult({
              status: 'success',
              message: '文件系统访问正常',
              details: `发现 ${listResult.data.files.length} 个文件/文件夹`
            });

            // 自动设置连接状态
            setCredentials(envNASConfig);
            setConnected(true, loginResult.sid);

            addResult('自动登录', 'success', '自动登录完成', '已自动连接到NAS，可以开始使用');
          } else {
            updateLastResult({
              status: 'failed',
              message: '文件系统访问失败',
              details: listResult.error || '无法访问文件系统'
            });
          }
        } catch (fileError) {
          updateLastResult({
            status: 'failed',
            message: '文件系统测试失败',
            details: fileError instanceof Error ? fileError.message : '未知错误'
          });
        }
      } else {
        updateLastResult({
          status: 'failed',
          message: '网络连接失败',
          details: loginResult.error || '连接超时或认证失败'
        });

        setError(loginResult.error || '自动连接失败');
      }
    } catch (error) {
      updateLastResult({
        status: 'failed',
        message: '连接测试失败',
        details: error instanceof Error ? error.message : '未知网络错误'
      });

      setError(error instanceof Error ? error.message : '自动连接失败');
    }

    setTesting(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 rounded-full bg-gray-300" />;
    }
  };

  if (!envNASConfig) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-orange-500" />
            环境配置缺失
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-muted-foreground mb-4">
              未找到完整的NAS配置信息，请检查 .env 文件
            </p>
            <div className="text-sm text-left bg-muted p-3 rounded font-mono">
              <div>需要的环境变量：</div>
              <div>VITE_NAS_URL=your_nas_url</div>
              <div>VITE_NAS_PORT=your_port</div>
              <div>VITE_NAS_USER=your_username</div>
              <div>VITE_NAS_PASSWORD=your_password</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5 text-blue-500" />
          自动连接测试
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
          <div className="text-sm">
            <div><strong>配置来源:</strong> .env 文件</div>
            <div><strong>NAS地址:</strong> {envConfig.nasUrl}</div>
            <div><strong>用户名:</strong> {envConfig.nasUser}</div>
            <div><strong>端口:</strong> {envConfig.nasPort}</div>
          </div>
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={runAutoTest} 
            disabled={testing}
            className="flex-1"
          >
            {testing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                测试中...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                重新测试连接
              </>
            )}
          </Button>
        </div>

        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-medium">测试结果:</h3>
            {results.map((result, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-md">
                {getStatusIcon(result.status)}
                <div className="flex-1">
                  <div className="font-medium text-sm">{result.step}</div>
                  <div className="text-sm text-muted-foreground">{result.message}</div>
                  {result.details && (
                    <div className="text-xs text-muted-foreground mt-1 font-mono bg-muted p-2 rounded">
                      {result.details}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, Loader2, AlertTriangle, Info } from 'lucide-react';

interface TestStep {
  name: string;
  status: 'pending' | 'running' | 'success' | 'failed' | 'warning';
  message: string;
  details?: string;
}

export const DetailedNetworkTest: React.FC = () => {
  const [host, setHost] = useState('*************');
  const [port, setPort] = useState(27323);
  const [testing, setTesting] = useState(false);
  const [steps, setSteps] = useState<TestStep[]>([]);

  const updateStep = (index: number, updates: Partial<TestStep>) => {
    setSteps(prev => prev.map((step, i) => i === index ? { ...step, ...updates } : step));
  };

  const addStep = (step: TestStep) => {
    setSteps(prev => [...prev, step]);
  };

  const runDetailedTest = async () => {
    setTesting(true);
    setSteps([]);

    // Step 1: 基础网络测试
    addStep({
      name: '1. 基础网络连通性测试',
      status: 'running',
      message: '测试是否能够到达目标主机...'
    });

    try {
      // 使用简单的fetch测试基础连通性
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);
      
      await fetch(`http://${host}:${port}`, {
        method: 'HEAD',
        signal: controller.signal,
        mode: 'no-cors'
      });
      
      clearTimeout(timeoutId);
      updateStep(0, {
        status: 'success',
        message: '基础网络连通性正常',
        details: `能够到达 ${host}:${port}`
      });
    } catch (error) {
      updateStep(0, {
        status: 'failed',
        message: '基础网络连通性失败',
        details: error instanceof Error ? error.message : '未知错误'
      });
    }

    // Step 2: 端口扫描
    addStep({
      name: '2. 常用端口扫描',
      status: 'running',
      message: '扫描常用的群晖端口...'
    });

    const commonPorts = [5000, 5001, 27323, 80, 443, 8080];
    const openPorts: number[] = [];

    for (const testPort of commonPorts) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 2000);
        
        await fetch(`http://${host}:${testPort}`, {
          method: 'HEAD',
          signal: controller.signal,
          mode: 'no-cors'
        });
        
        clearTimeout(timeoutId);
        openPorts.push(testPort);
      } catch (error) {
        // 端口不可达，继续测试下一个
      }
    }

    updateStep(1, {
      status: openPorts.length > 0 ? 'success' : 'warning',
      message: `发现 ${openPorts.length} 个开放端口`,
      details: openPorts.length > 0 ? `开放端口: ${openPorts.join(', ')}` : '未发现开放端口'
    });

    // Step 3: DSM Web界面测试
    addStep({
      name: '3. DSM Web界面测试',
      status: 'running',
      message: '测试DSM管理界面访问...'
    });

    let dsmFound = false;
    const dsmPorts = [5000, 5001, port];

    for (const dsmPort of dsmPorts) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const response = await fetch(`http://${host}:${dsmPort}`, {
          method: 'GET',
          signal: controller.signal,
          mode: 'cors'
        });
        
        clearTimeout(timeoutId);
        
        if (response.ok) {
          dsmFound = true;
          updateStep(2, {
            status: 'success',
            message: `DSM界面可访问 (端口 ${dsmPort})`,
            details: `HTTP状态: ${response.status}`
          });
          break;
        }
      } catch (error) {
        // 继续测试下一个端口
      }
    }

    if (!dsmFound) {
      updateStep(2, {
        status: 'failed',
        message: 'DSM Web界面不可访问',
        details: '尝试的端口: ' + dsmPorts.join(', ')
      });
    }

    // Step 4: WebAPI测试
    addStep({
      name: '4. WebAPI可用性测试',
      status: 'running',
      message: '测试群晖WebAPI...'
    });

    try {
      const apiUrl = `http://${host}:${port}/webapi/query.cgi?api=SYNO.API.Info&version=1&method=query&query=SYNO.API.Auth`;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        signal: controller.signal,
        mode: 'cors'
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        const data = await response.json();
        updateStep(3, {
          status: data.success ? 'success' : 'warning',
          message: data.success ? 'WebAPI正常工作' : 'WebAPI响应异常',
          details: `响应: ${JSON.stringify(data).substring(0, 100)}...`
        });
      } else {
        updateStep(3, {
          status: 'failed',
          message: 'WebAPI请求失败',
          details: `HTTP状态: ${response.status}`
        });
      }
    } catch (error) {
      updateStep(3, {
        status: 'failed',
        message: 'WebAPI连接失败',
        details: error instanceof Error ? error.message : '未知错误'
      });
    }

    // Step 5: 建议和总结
    addStep({
      name: '5. 诊断建议',
      status: 'pending',
      message: '生成诊断建议...'
    });

    // 分析结果并给出建议
    const hasNetworkIssue = steps[0]?.status === 'failed';
    const hasOpenPorts = openPorts.length > 0;
    const hasDSM = dsmFound;

    let suggestion = '';
    let suggestionStatus: TestStep['status'] = 'info';

    if (hasNetworkIssue) {
      suggestion = '网络连接问题：请检查NAS是否开机，网络连接是否正常';
      suggestionStatus = 'failed';
    } else if (!hasOpenPorts) {
      suggestion = '端口问题：所有测试端口都无法访问，请检查防火墙设置';
      suggestionStatus = 'warning';
    } else if (!hasDSM) {
      suggestion = '服务问题：网络正常但DSM服务不可访问，请检查DSM服务状态';
      suggestionStatus = 'warning';
    } else {
      suggestion = '连接正常：建议使用发现的开放端口进行连接';
      suggestionStatus = 'success';
    }

    updateStep(4, {
      status: suggestionStatus,
      message: suggestion,
      details: `建议端口: ${openPorts.length > 0 ? openPorts.join(', ') : '5000, 5001'}`
    });

    setTesting(false);
  };

  const getStatusIcon = (status: TestStep['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>详细网络诊断</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="diag-host">NAS IP地址</Label>
            <Input
              id="diag-host"
              value={host}
              onChange={(e) => setHost(e.target.value)}
              placeholder="*************"
            />
          </div>
          <div>
            <Label htmlFor="diag-port">主要端口</Label>
            <Input
              id="diag-port"
              type="number"
              value={port}
              onChange={(e) => setPort(parseInt(e.target.value) || 27323)}
              placeholder="27323"
            />
          </div>
        </div>

        <Button 
          onClick={runDetailedTest} 
          disabled={testing}
          className="w-full"
        >
          {testing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              运行详细诊断...
            </>
          ) : (
            '开始详细网络诊断'
          )}
        </Button>

        {steps.length > 0 && (
          <div className="space-y-3">
            {steps.map((step, index) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex items-start gap-3">
                  {getStatusIcon(step.status)}
                  <div className="flex-1">
                    <div className="font-medium text-sm">{step.name}</div>
                    <div className="text-sm text-muted-foreground">{step.message}</div>
                    {step.details && (
                      <div className="text-xs text-muted-foreground mt-1 font-mono bg-muted p-2 rounded">
                        {step.details}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

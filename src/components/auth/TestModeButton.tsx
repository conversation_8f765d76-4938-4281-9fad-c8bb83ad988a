import React from 'react';
import { Button } from '@/components/ui/button';
import { useNASStore } from '@/store/nasStore';
import { NASFileInfo } from '@/types/nas';
import { TestTube } from 'lucide-react';

export const TestModeButton: React.FC = () => {
  const { setCredentials, setConnected, setFiles, setCurrentPath } = useNASStore();

  const enterTestMode = () => {
    // Set mock credentials
    setCredentials({
      host: 'demo-nas.local',
      port: 5000,
      username: 'demo',
      password: 'demo',
      useHttps: false,
    });

    // Set connected state with mock SID
    setConnected(true, 'mock-session-id');

    // Set mock files
    const mockFiles: NASFileInfo[] = [
      {
        name: 'Albums',
        path: '/music/Albums',
        isdir: true,
      },
      {
        name: 'Artists',
        path: '/music/Artists',
        isdir: true,
      },
      {
        name: 'Playlists',
        path: '/music/Playlists',
        isdir: true,
      },
      {
        name: 'Sample Song 1.mp3',
        path: '/music/Sample Song 1.mp3',
        isdir: false,
        size: 5242880, // 5MB
      },
      {
        name: 'Sample Song 2.flac',
        path: '/music/Sample Song 2.flac',
        isdir: false,
        size: 31457280, // 30MB
      },
      {
        name: 'Sample Song 3.m4a',
        path: '/music/Sample Song 3.m4a',
        isdir: false,
        size: 4194304, // 4MB
      },
    ];

    setFiles(mockFiles);
    setCurrentPath('/music');
  };

  return (
    <Button
      variant="outline"
      onClick={enterTestMode}
      className="w-full mt-4"
    >
      <TestTube className="mr-2 h-4 w-4" />
      Enter Test Mode (Demo)
    </Button>
  );
};

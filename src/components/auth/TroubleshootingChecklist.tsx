import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Circle, AlertTriangle, ExternalLink } from 'lucide-react';

interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  checked: boolean;
  critical: boolean;
  helpUrl?: string;
}

export const TroubleshootingChecklist: React.FC<{ host: string; port: number }> = ({ host, port }) => {
  const [items, setItems] = useState<ChecklistItem[]>([
    {
      id: 'nas-power',
      title: 'NAS电源状态',
      description: '确认群晖NAS已开机，前面板指示灯正常',
      checked: false,
      critical: true
    },
    {
      id: 'network-cable',
      title: '网络连接',
      description: '确认网线连接正常，网络指示灯亮起',
      checked: false,
      critical: true
    },
    {
      id: 'same-network',
      title: '同一网络',
      description: '确认电脑和NAS在同一局域网内',
      checked: false,
      critical: true
    },
    {
      id: 'ip-address',
      title: 'IP地址正确',
      description: `确认NAS的IP地址确实是 ${host}`,
      checked: false,
      critical: true
    },
    {
      id: 'dsm-running',
      title: 'DSM服务运行',
      description: '确认DSM系统正常启动，没有卡在启动界面',
      checked: false,
      critical: true
    },
    {
      id: 'port-config',
      title: '端口配置',
      description: `确认DSM配置的端口是 ${port}（控制面板 > 网络 > DSM设置）`,
      checked: false,
      critical: true
    },
    {
      id: 'firewall-nas',
      title: 'NAS防火墙',
      description: '检查NAS防火墙是否允许相应端口访问',
      checked: false,
      critical: false
    },
    {
      id: 'firewall-router',
      title: '路由器防火墙',
      description: '检查路由器防火墙设置，确保没有阻止内网通信',
      checked: false,
      critical: false
    },
    {
      id: 'web-station',
      title: 'Web Station服务',
      description: '确认Web Station套件已安装并启用',
      checked: false,
      critical: false
    },
    {
      id: 'user-permissions',
      title: '用户权限',
      description: '确认用户账号有足够的权限访问文件和服务',
      checked: false,
      critical: false
    }
  ]);

  const toggleItem = (id: string) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, checked: !item.checked } : item
    ));
  };

  const criticalIssues = items.filter(item => item.critical && !item.checked);
  const allCriticalChecked = criticalIssues.length === 0;

  const getRecommendation = () => {
    if (criticalIssues.length > 0) {
      return {
        type: 'error',
        message: `还有 ${criticalIssues.length} 个关键问题需要检查`,
        details: '请先解决标记为关键的问题'
      };
    } else {
      return {
        type: 'success',
        message: '所有关键检查项都已完成',
        details: '如果仍然无法连接，可能是网络配置或服务配置问题'
      };
    }
  };

  const recommendation = getRecommendation();

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          连接问题排查清单
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {items.map((item) => (
            <div 
              key={item.id}
              className={`flex items-start gap-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50 ${
                item.critical ? 'border-orange-200 bg-orange-50/50 dark:border-orange-800 dark:bg-orange-950/50' : ''
              }`}
              onClick={() => toggleItem(item.id)}
            >
              <div className="mt-0.5">
                {item.checked ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <Circle className="h-5 w-5 text-gray-400" />
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className={`font-medium ${item.checked ? 'line-through text-muted-foreground' : ''}`}>
                    {item.title}
                  </span>
                  {item.critical && (
                    <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                      关键
                    </span>
                  )}
                </div>
                <p className={`text-sm text-muted-foreground mt-1 ${item.checked ? 'line-through' : ''}`}>
                  {item.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className={`p-4 rounded-lg ${
          recommendation.type === 'error' 
            ? 'bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800' 
            : 'bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800'
        }`}>
          <div className="flex items-start gap-2">
            {recommendation.type === 'error' ? (
              <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
            ) : (
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            )}
            <div>
              <div className="font-medium">{recommendation.message}</div>
              <div className="text-sm text-muted-foreground mt-1">{recommendation.details}</div>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
          <h3 className="font-medium mb-2">快速验证方法</h3>
          <div className="space-y-2 text-sm">
            <div>
              <strong>1. 浏览器测试：</strong>
              <div className="flex gap-2 mt-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(`http://${host}:5000`, '_blank')}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  HTTP:5000
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(`https://${host}:5001`, '_blank')}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  HTTPS:5001
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(`http://${host}:${port}`, '_blank')}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  HTTP:{port}
                </Button>
              </div>
            </div>
            <div>
              <strong>2. 终端测试：</strong>
              <code className="block mt-1 p-2 bg-muted rounded text-xs">
                ping {host}<br/>
                telnet {host} {port}
              </code>
            </div>
          </div>
        </div>

        <div className="text-xs text-muted-foreground">
          <p><strong>提示：</strong>点击上方项目来标记已完成的检查。关键项目必须全部完成才能正常连接。</p>
        </div>
      </CardContent>
    </Card>
  );
};

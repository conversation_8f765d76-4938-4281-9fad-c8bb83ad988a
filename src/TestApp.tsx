import React from 'react'

function TestApp() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Tauri Music Player - 测试页面</h1>
      <div style={{ marginTop: '20px' }}>
        <h2>环境变量测试:</h2>
        <ul>
          <li>NAS_URL: {import.meta.env.VITE_NAS_URL || '未设置'}</li>
          <li>NAS_PORT: {import.meta.env.VITE_NAS_PORT || '未设置'}</li>
          <li>NAS_USER: {import.meta.env.VITE_NAS_USER || '未设置'}</li>
          <li>NAS_PASSWORD: {import.meta.env.VITE_NAS_PASSWORD ? '已设置' : '未设置'}</li>
        </ul>
      </div>
      <div style={{ marginTop: '20px' }}>
        <p>如果您能看到这个页面，说明基础功能正常。</p>
        <p>接下来我们可以逐步添加更多功能。</p>
      </div>
    </div>
  )
}

export default TestApp

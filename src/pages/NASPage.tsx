import React from 'react';
import { useNASStore } from '@/store/nasStore';
import { LoginForm } from '@/components/auth/LoginForm';
import { FileBrowser } from '@/components/nas/FileBrowser';
import { MainLayout } from '@/components/layout/MainLayout';

export const NASPage: React.FC = () => {
  const { isConnected } = useNASStore();

  return (
    <MainLayout>
      {isConnected ? <FileBrowser /> : <LoginForm />}
    </MainLayout>
  );
};

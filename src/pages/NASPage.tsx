import React from 'react';
import { useNASStore } from '@/store/nasStore';
import { LoginForm } from '@/components/auth/LoginForm';
import { FileBrowser } from '@/components/nas/FileBrowser';
import { MainLayout } from '@/components/layout/MainLayout';
import { AutoConnectionTest } from '@/components/auth/AutoConnectionTest';
import { getEnvNASConfig } from '@/utils/envConfig';

export const NASPage: React.FC = () => {
  const { isConnected } = useNASStore();
  const envConfig = getEnvNASConfig();

  // 如果有环境变量配置且未连接，显示自动连接测试
  if (!isConnected && envConfig) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2">Tauri Music Player</h1>
            <p className="text-muted-foreground">基于群晖NAS的音乐播放器</p>
          </div>
          <AutoConnectionTest />
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      {isConnected ? <FileBrowser /> : <LoginForm />}
    </MainLayout>
  );
};

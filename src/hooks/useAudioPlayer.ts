import { useEffect, useRef, useCallback } from 'react';
import { useAudioStore } from '@/store/audioStore';
import { convertFileSrc } from '@tauri-apps/api/core';

export const useAudioPlayer = () => {
  const audioRef = useRef<HTMLAudioElement>(new Audio());
  const {
    currentSong,
    isPlaying,
    volume,
    currentTime,
    setCurrentTime,
    setDuration,
    nextSong,
    pauseSong,
  } = useAudioStore();

  // Update audio source when current song changes
  useEffect(() => {
    const audio = audioRef.current;
    if (currentSong) {
      const assetUrl = convertFileSrc(currentSong.file_path);
      audio.src = assetUrl;
      audio.load();
    }
  }, [currentSong]);

  // Handle play/pause
  useEffect(() => {
    const audio = audioRef.current;
    if (isPlaying && currentSong) {
      audio.play().catch(console.error);
    } else {
      audio.pause();
    }
  }, [isPlaying, currentSong]);

  // Handle volume changes
  useEffect(() => {
    audioRef.current.volume = volume / 100;
  }, [volume]);

  // Set up audio event listeners
  useEffect(() => {
    const audio = audioRef.current;

    const updateTime = () => {
      setCurrentTime(audio.currentTime);
    };

    const updateDuration = () => {
      setDuration(audio.duration || 0);
    };

    const handleEnded = () => {
      nextSong();
    };

    const handleError = (e: Event) => {
      console.error('Audio error:', e);
      pauseSong();
    };

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
    };
  }, [setCurrentTime, setDuration, nextSong, pauseSong]);

  const seekTo = useCallback((time: number) => {
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  }, [setCurrentTime]);

  return {
    seekTo,
    audioElement: audioRef.current,
  };
};

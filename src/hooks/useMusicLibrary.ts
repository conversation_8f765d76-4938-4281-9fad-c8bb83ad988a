import { useEffect } from 'react';
import { useLibraryStore } from '@/store/libraryStore';
import { musicAPI } from '@/utils/tauri';

export const useMusicLibrary = () => {
  const { 
    songs, 
    loading, 
    error, 
    setSongs, 
    setLoading, 
    setError,
    addSong 
  } = useLibraryStore();

  const loadSongs = async () => {
    try {
      setLoading(true);
      setError(null);
      const allSongs = await musicAPI.getAllSongs();
      setSongs(allSongs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load songs');
    } finally {
      setLoading(false);
    }
  };

  const scanFolder = async (folderPath: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const musicFiles = await musicAPI.scanMusicFolder(folderPath);
      
      for (const filePath of musicFiles) {
        try {
          const metadata = await musicAPI.getAudioMetadata(filePath);
          const [fileSize] = await musicAPI.getFileInfo(filePath);
          
          const newSong = {
            title: metadata.title || 'Unknown Title',
            artist: metadata.artist,
            album: metadata.album,
            genre: metadata.genre,
            year: metadata.year,
            duration: metadata.duration,
            file_path: filePath,
            file_size: fileSize,
            bit_rate: metadata.bit_rate,
            sample_rate: metadata.sample_rate,
            date_added: new Date().toISOString(),
            play_count: 0,
          };
          
          const savedSong = await musicAPI.addSong(newSong);
          addSong(savedSong);
        } catch (fileError) {
          console.error(`Failed to process file ${filePath}:`, fileError);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to scan folder');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSongs();
  }, []);

  return {
    songs,
    loading,
    error,
    loadSongs,
    scanFolder,
  };
};

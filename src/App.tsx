import React, { useState } from 'react'
import TestApp from './TestApp'

function App() {
  const [showAdvanced, setShowAdvanced] = useState(false)

  if (!showAdvanced) {
    return (
      <div>
        <TestApp />
        <div style={{ padding: '20px', borderTop: '1px solid #ccc', marginTop: '20px' }}>
          <button
            onClick={() => setShowAdvanced(true)}
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            启用高级功能 (NAS连接测试)
          </button>
        </div>
      </div>
    )
  }

  // 高级功能版本
  return <AdvancedApp />
}

// 高级功能组件
function AdvancedApp() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>高级功能模式</h1>
      <p>正在加载NAS连接功能...</p>
      <button
        onClick={() => window.location.reload()}
        style={{
          padding: '10px 20px',
          backgroundColor: '#6c757d',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        返回基础模式
      </button>
    </div>
  )
}

export default App

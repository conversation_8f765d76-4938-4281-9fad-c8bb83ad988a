import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MainLayout } from '@/components/layout/MainLayout'
import { SongList } from '@/components/library/SongList'
import { useMusicLibrary } from '@/hooks/useMusicLibrary'
import { FolderOpen, RefreshCw } from 'lucide-react'
import { open } from '@tauri-apps/plugin-opener'

function App() {
  const { loading, error, loadSongs, scanFolder } = useMusicLibrary()
  const [scanning, setScanning] = useState(false)

  const handleScanFolder = async () => {
    try {
      setScanning(true)
      // For now, we'll use a hardcoded path. In a real app, you'd use a folder picker
      const testPath = '/Users' // You can change this to a path that exists on your system
      await scanFolder(testPath)
    } catch (err) {
      console.error('Failed to scan folder:', err)
    } finally {
      setScanning(false)
    }
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Tauri Music Player</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button onClick={handleScanFolder} disabled={scanning || loading} className="flex items-center gap-2">
                <FolderOpen className="h-4 w-4" />
                {scanning ? 'Scanning...' : 'Scan Music Folder'}
              </Button>

              <Button variant="outline" onClick={loadSongs} disabled={loading} className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Refresh Library
              </Button>
            </div>

            {error && <div className="mt-4 p-3 bg-destructive/10 text-destructive rounded-md">{error}</div>}
          </CardContent>
        </Card>

        <SongList />
      </div>
    </MainLayout>
  )
}

export default App

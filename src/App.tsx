import React, { useState } from 'react'
import TestApp from './TestApp'

function App() {
  const [showAdvanced, setShowAdvanced] = useState(false)

  if (!showAdvanced) {
    return (
      <div>
        <TestApp />
        <div style={{ padding: '20px', borderTop: '1px solid #ccc', marginTop: '20px' }}>
          <button
            onClick={() => setShowAdvanced(true)}
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            启用高级功能 (NAS连接测试)
          </button>
        </div>
      </div>
    )
  }

  // 高级功能版本
  return <AdvancedApp />
}

// 高级功能组件
function AdvancedApp() {
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<string[]>([])
  const [connected, setConnected] = useState(false)

  const runNASTest = async () => {
    setTesting(true)
    setResults([])

    const addResult = (message: string) => {
      setResults((prev) => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
    }

    try {
      addResult('开始NAS连接测试...')

      // 读取环境变量
      const nasUrl = import.meta.env.VITE_NAS_URL
      const nasPort = import.meta.env.VITE_NAS_PORT
      const nasUser = import.meta.env.VITE_NAS_USER
      const nasPassword = import.meta.env.VITE_NAS_PASSWORD

      addResult(`配置信息: ${nasUrl}:${nasPort}, 用户: ${nasUser}`)

      if (!nasUrl || !nasUser || !nasPassword) {
        addResult('❌ 环境变量配置不完整')
        return
      }

      // 解析QuickConnect地址
      let host = ''
      let useHttps = false
      let port = 5000

      if (nasUrl.includes('quickconnect.cn')) {
        // QuickConnect地址处理
        host = nasUrl.replace(/https?:\/\//, '').split('/')[0]
        useHttps = true
        port = 443
        addResult(`✅ QuickConnect地址: ${host}`)

        try {
          const connectivityResult = await invoke('test_quickconnect_connectivity', { host })
          addResult(`✅ 连通性测试: ${connectivityResult}`)
        } catch (connectError) {
          addResult(`⚠️ 连通性测试: ${connectError}`)
        }
      } else {
        // 内网IP地址处理
        try {
          const url = new URL(nasUrl)
          host = url.hostname
          useHttps = url.protocol === 'https:'
          port = url.port ? parseInt(url.port) : (useHttps ? 5001 : 5000)

          // 如果环境变量指定了端口，使用环境变量的端口
          if (nasPort) {
            port = parseInt(nasPort)
          }

          addResult(`✅ 内网地址解析: ${useHttps ? 'https' : 'http'}://${host}:${port}`)
        } catch (urlError) {
          addResult(`❌ 地址解析失败: ${urlError}`)
          return
        }
      }

      // 使用Tauri命令进行连接测试
      addResult('🔄 正在通过Tauri后端连接...')

      // 这里我们需要调用Tauri命令
      const { invoke } = await import('@tauri-apps/api/core')

      const loginRequest = {
        host,
        port,
        username: nasUser,
        password: nasPassword,
        use_https: useHttps
      }

      const loginResult = await invoke('nas_login', { request: loginRequest })

      if (loginResult.success) {
        addResult('✅ NAS登录成功!')
        addResult(`会话ID: ${loginResult.sid?.substring(0, 8)}...`)
        setConnected(true)

        // 测试文件列表
        addResult('🔄 测试文件系统访问...')
        const listResult = await invoke('nas_list_files', {
          host,
          port,
          useHttps,
          sid: loginResult.sid,
          path: '/'
        })

        if (listResult.success) {
          addResult(`✅ 文件系统访问成功! 发现 ${listResult.files?.length || 0} 个项目`)
        } else {
          addResult(`⚠️ 文件系统访问失败: ${listResult.error}`)
        }
      } else {
        addResult(`❌ NAS登录失败: ${loginResult.error}`)
      }
    } catch (error) {
      addResult(`❌ 连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setTesting(false)
    }
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ marginBottom: '20px' }}>
        <h1>NAS连接测试</h1>
        <p>基于您的 .env 配置进行自动连接测试</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={runNASTest}
          disabled={testing}
          style={{
            padding: '12px 24px',
            backgroundColor: testing ? '#6c757d' : '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: testing ? 'not-allowed' : 'pointer',
            marginRight: '10px'
          }}
        >
          {testing ? '测试中...' : '开始NAS连接测试'}
        </button>

        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '12px 24px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer'
          }}
        >
          返回基础模式
        </button>
      </div>

      {connected && (
        <div
          style={{
            padding: '15px',
            backgroundColor: '#d4edda',
            border: '1px solid #c3e6cb',
            borderRadius: '6px',
            marginBottom: '20px'
          }}
        >
          <strong>🎉 连接成功!</strong> 您的NAS已成功连接，可以开始使用音乐播放功能。
        </div>
      )}

      {results.length > 0 && (
        <div
          style={{
            border: '1px solid #ddd',
            borderRadius: '6px',
            padding: '15px',
            backgroundColor: '#f8f9fa'
          }}
        >
          <h3>测试日志:</h3>
          <div
            style={{
              fontFamily: 'monospace',
              fontSize: '14px',
              maxHeight: '400px',
              overflowY: 'auto',
              backgroundColor: '#fff',
              padding: '10px',
              border: '1px solid #eee',
              borderRadius: '4px'
            }}
          >
            {results.map((result, index) => (
              <div key={index} style={{ marginBottom: '5px' }}>
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default App

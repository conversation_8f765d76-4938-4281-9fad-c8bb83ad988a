export interface Song {
  id: number;
  title: string;
  artist?: string;
  album?: string;
  genre?: string;
  year?: number;
  duration: number; // in seconds
  file_path: string;
  file_size?: number;
  bit_rate?: number;
  sample_rate?: number;
  cover_art_path?: string;
  date_added: string;
  date_modified?: string;
  play_count: number;
  last_played?: string;
}

export interface AudioMetadata {
  title?: string;
  artist?: string;
  album?: string;
  genre?: string;
  year?: number;
  duration: number;
  bit_rate?: number;
  sample_rate?: number;
}

export type PlayMode = 'sequence' | 'repeat' | 'shuffle';

export interface AudioState {
  currentSong: Song | null;
  isPlaying: boolean;
  volume: number;
  currentTime: number;
  duration: number;
  playMode: PlayMode;
  queue: Song[];
  currentIndex: number;
}

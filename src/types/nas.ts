export interface NASCredentials {
  host: string;
  port: number;
  username: string;
  password: string;
  useHttps: boolean;
}

export interface NASLoginResponse {
  success: boolean;
  sid?: string;
  error?: string;
}

export interface NASFileInfo {
  name: string;
  path: string;
  isdir: boolean;
  size?: number;
  mt_time?: number;
  additional?: {
    time?: {
      mtime?: number;
      atime?: number;
      ctime?: number;
    };
    perm?: {
      posix?: number;
    };
  };
}

export interface NASDirectoryResponse {
  success: boolean;
  data?: {
    files: NASFileInfo[];
    offset: number;
    total: number;
  };
  error?: string;
}

export interface NASConnectionState {
  isConnected: boolean;
  credentials: NASCredentials | null;
  sid: string | null;
  error: string | null;
}

export interface DatabaseSong {
  id?: number;
  title: string;
  artist?: string;
  album?: string;
  genre?: string;
  year?: number;
  duration: number;
  file_path: string;
  file_size?: number;
  bit_rate?: number;
  sample_rate?: number;
  cover_art_path?: string;
  date_added?: string;
  date_modified?: string;
  play_count?: number;
  last_played?: string;
}

export interface DatabasePlaylist {
  id?: number;
  name: string;
  description?: string;
  cover_image?: string;
  created_at?: string;
  updated_at?: string;
  song_count?: number;
}

export interface Settings {
  key: string;
  value: string;
  updated_at?: string;
}

export interface PlayHistory {
  id?: number;
  song_id: number;
  played_at?: string;
  play_duration?: number;
}

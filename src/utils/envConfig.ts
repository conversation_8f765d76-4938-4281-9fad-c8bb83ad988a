import { NASCredentials } from '@/types/nas';

export const getEnvNASConfig = (): NASCredentials | null => {
  const nasUrl = import.meta.env.VITE_NAS_URL;
  const nasPort = import.meta.env.VITE_NAS_PORT;
  const nasUser = import.meta.env.VITE_NAS_USER;
  const nasPassword = import.meta.env.VITE_NAS_PASSWORD;

  if (!nasUrl || !nasUser || !nasPassword) {
    return null;
  }

  // 解析QuickConnect URL
  let host = '';
  let useHttps = false;
  let port = 5000;

  try {
    const url = new URL(nasUrl);
    host = url.hostname;
    useHttps = url.protocol === 'https:';
    port = url.port ? parseInt(url.port) : (useHttps ? 5001 : 5000);
    
    // 如果有环境变量指定的端口，使用环境变量的端口
    if (nasPort) {
      port = parseInt(nasPort);
    }
  } catch (error) {
    // 如果URL解析失败，尝试直接使用域名
    if (nasUrl.includes('quickconnect.cn')) {
      host = nasUrl.replace(/https?:\/\//, '').split('/')[0].split(':')[0];
      useHttps = nasUrl.startsWith('https');
      port = nasPort ? parseInt(nasPort) : 27323;
    } else {
      console.error('Failed to parse NAS URL:', error);
      return null;
    }
  }

  return {
    host,
    port,
    username: nasUser,
    password: nasPassword,
    useHttps,
  };
};

export const envConfig = {
  nasUrl: import.meta.env.VITE_NAS_URL,
  nasPort: import.meta.env.VITE_NAS_PORT,
  nasUser: import.meta.env.VITE_NAS_USER,
  nasPassword: import.meta.env.VITE_NAS_PASSWORD,
};

import { invoke } from '@tauri-apps/api/core';
import { NASCredentials, NASLoginResponse, NASDirectoryResponse, NASFileInfo } from '@/types/nas';

interface TauriNASLoginRequest {
  host: string;
  port: number;
  username: string;
  password: string;
  use_https: boolean;
}

interface TauriNASLoginResponse {
  success: boolean;
  sid?: string;
  error?: string;
}

interface TauriNASFileInfo {
  name: string;
  path: string;
  isdir: boolean;
  size?: number;
  mt_time?: number;
}

interface TauriNASListResponse {
  success: boolean;
  files?: TauriNASFileInfo[];
  error?: string;
}

export class NASApiTauri {
  private credentials: NASCredentials | null = null;
  private sid: string | null = null;

  async login(credentials: NASCredentials): Promise<NASLoginResponse> {
    try {
      const request: TauriNASLoginRequest = {
        host: credentials.host,
        port: credentials.port,
        username: credentials.username,
        password: credentials.password,
        use_https: credentials.useHttps,
      };

      const response: TauriNASLoginResponse = await invoke('nas_login', { request });
      
      if (response.success && response.sid) {
        this.credentials = credentials;
        this.sid = response.sid;
        return { success: true, sid: response.sid };
      } else {
        return { 
          success: false, 
          error: response.error || 'Login failed' 
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  async logout(): Promise<void> {
    this.credentials = null;
    this.sid = null;
  }

  async listFiles(path: string = '/music'): Promise<NASDirectoryResponse> {
    if (!this.credentials || !this.sid) {
      return { success: false, error: 'Not authenticated' };
    }

    try {
      const response: TauriNASListResponse = await invoke('nas_list_files', {
        host: this.credentials.host,
        port: this.credentials.port,
        useHttps: this.credentials.useHttps,
        sid: this.sid,
        path: path,
      });

      if (response.success && response.files) {
        // 转换文件格式
        const files: NASFileInfo[] = response.files.map(file => ({
          name: file.name,
          path: file.path,
          isdir: file.isdir,
          size: file.size,
          mt_time: file.mt_time,
        }));

        return {
          success: true,
          data: {
            files,
            offset: 0,
            total: files.length,
          }
        };
      } else {
        return { 
          success: false, 
          error: response.error || 'Failed to list files' 
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: `Failed to list files: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  async getFileUrl(filePath: string): Promise<string | null> {
    if (!this.credentials || !this.sid) {
      return null;
    }

    try {
      const url: string = await invoke('nas_get_download_url', {
        host: this.credentials.host,
        port: this.credentials.port,
        useHttps: this.credentials.useHttps,
        sid: this.sid,
        filePath: filePath,
      });

      return url;
    } catch (error) {
      console.error('Failed to get file URL:', error);
      return null;
    }
  }

  setSid(sid: string) {
    this.sid = sid;
  }

  getCredentials(): NASCredentials | null {
    return this.credentials;
  }
}

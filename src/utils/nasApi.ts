import { NASCredentials, NASLoginResponse, NASDirectoryResponse } from '@/types/nas'

export class NASApi {
  private baseUrl: string
  private sid: string | null = null

  constructor(credentials: NASCredentials) {
    const protocol = credentials.useHttps ? 'https' : 'http'
    this.baseUrl = `${protocol}://${credentials.host}:${credentials.port}`
  }

  async login(credentials: NASCredentials): Promise<NASLoginResponse> {
    try {
      // First, try to test basic connectivity
      const testUrl = `${this.baseUrl}/webapi/query.cgi?api=SYNO.API.Info&version=1&method=query&query=SYNO.API.Auth`

      console.log(`Testing connection to: ${this.baseUrl}`)
      console.log(`Test URL: ${testUrl}`)

      // Test basic connectivity first
      try {
        const testResponse = await fetch(testUrl, {
          method: 'GET',
          mode: 'cors',
          headers: {
            Accept: 'application/json'
          }
        })

        if (!testResponse.ok) {
          throw new Error(`HTTP ${testResponse.status}: ${testResponse.statusText}`)
        }

        const testData = await testResponse.json()
        console.log('API Info response:', testData)

        if (!testData.success) {
          throw new Error('API Info query failed')
        }
      } catch (testError) {
        console.error('Connectivity test failed:', testError)
        return {
          success: false,
          error: `Cannot connect to NAS at ${
            this.baseUrl
          }. Please check:\n1. IP address and port are correct\n2. NAS is powered on and accessible\n3. Web Station/DSM is running\n4. Firewall allows connections\n\nError: ${
            testError instanceof Error ? testError.message : 'Unknown error'
          }`
        }
      }

      // Now try authentication
      const authUrl = `${this.baseUrl}/webapi/auth.cgi`
      console.log(`Attempting login to: ${authUrl}`)

      const response = await fetch(authUrl, {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Accept: 'application/json'
        },
        body: new URLSearchParams({
          api: 'SYNO.API.Auth',
          version: '3',
          method: 'login',
          account: credentials.username,
          passwd: credentials.password,
          session: 'FileStation',
          format: 'sid'
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('Login response:', data)

      if (data.success) {
        this.sid = data.data.sid
        return { success: true, sid: this.sid }
      } else {
        return {
          success: false,
          error: this.getErrorMessage(data.error?.code)
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: `Connection failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }\n\nTroubleshooting:\n1. Verify NAS IP: ${credentials.host}\n2. Check port: ${
          credentials.port
        }\n3. Ensure DSM is accessible via web browser\n4. Try different protocol (HTTP/HTTPS)`
      }
    }
  }

  async logout(): Promise<void> {
    if (!this.sid) return

    try {
      await fetch(`${this.baseUrl}/webapi/auth.cgi`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          api: 'SYNO.API.Auth',
          version: '3',
          method: 'logout',
          session: 'FileStation',
          _sid: this.sid
        })
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      this.sid = null
    }
  }

  async listFiles(path: string = '/music'): Promise<NASDirectoryResponse> {
    if (!this.sid) {
      return { success: false, error: 'Not authenticated' }
    }

    try {
      const response = await fetch(`${this.baseUrl}/webapi/entry.cgi`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          api: 'SYNO.FileStation.List',
          version: '2',
          method: 'list',
          folder_path: path,
          additional: '["time","perm"]',
          _sid: this.sid
        })
      })

      const data = await response.json()

      if (data.success) {
        return {
          success: true,
          data: {
            files: data.data.files,
            offset: data.data.offset || 0,
            total: data.data.total || data.data.files.length
          }
        }
      } else {
        return {
          success: false,
          error: this.getErrorMessage(data.error?.code)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to list files: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  async getFileUrl(path: string): Promise<string | null> {
    if (!this.sid) return null

    return `${
      this.baseUrl
    }/webapi/entry.cgi?api=SYNO.FileStation.Download&version=2&method=download&path=${encodeURIComponent(
      path
    )}&mode=download&_sid=${this.sid}`
  }

  private getErrorMessage(code?: number): string {
    const errorMessages: Record<number, string> = {
      400: 'No such account or incorrect password',
      401: 'Account disabled',
      402: 'Permission denied',
      403: 'Two-step verification code required',
      404: 'Failed to authenticate 2-step verification code',
      406: 'Enforce to authenticate with 2-step verification code',
      407: 'Blocked IP source',
      408: 'Expired password cannot change',
      409: 'Expired password',
      410: 'Password must be changed'
    }

    return errorMessages[code || 0] || `Unknown error (code: ${code})`
  }

  setSid(sid: string) {
    this.sid = sid
  }
}

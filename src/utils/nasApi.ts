import { NASCredentials, NASLoginResponse, NASDirectoryResponse } from '@/types/nas';

export class NASApi {
  private baseUrl: string;
  private sid: string | null = null;

  constructor(credentials: NASCredentials) {
    const protocol = credentials.useHttps ? 'https' : 'http';
    this.baseUrl = `${protocol}://${credentials.host}:${credentials.port}`;
  }

  async login(credentials: NASCredentials): Promise<NASLoginResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/webapi/auth.cgi`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          api: 'SYNO.API.Auth',
          version: '3',
          method: 'login',
          account: credentials.username,
          passwd: credentials.password,
          session: 'FileStation',
          format: 'sid',
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        this.sid = data.data.sid;
        return { success: true, sid: this.sid };
      } else {
        return { 
          success: false, 
          error: this.getErrorMessage(data.error?.code) 
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  async logout(): Promise<void> {
    if (!this.sid) return;

    try {
      await fetch(`${this.baseUrl}/webapi/auth.cgi`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          api: 'SYNO.API.Auth',
          version: '3',
          method: 'logout',
          session: 'FileStation',
          _sid: this.sid,
        }),
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.sid = null;
    }
  }

  async listFiles(path: string = '/music'): Promise<NASDirectoryResponse> {
    if (!this.sid) {
      return { success: false, error: 'Not authenticated' };
    }

    try {
      const response = await fetch(`${this.baseUrl}/webapi/entry.cgi`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          api: 'SYNO.FileStation.List',
          version: '2',
          method: 'list',
          folder_path: path,
          additional: '["time","perm"]',
          _sid: this.sid,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        return {
          success: true,
          data: {
            files: data.data.files,
            offset: data.data.offset || 0,
            total: data.data.total || data.data.files.length,
          }
        };
      } else {
        return { 
          success: false, 
          error: this.getErrorMessage(data.error?.code) 
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: `Failed to list files: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  async getFileUrl(path: string): Promise<string | null> {
    if (!this.sid) return null;
    
    return `${this.baseUrl}/webapi/entry.cgi?api=SYNO.FileStation.Download&version=2&method=download&path=${encodeURIComponent(path)}&mode=download&_sid=${this.sid}`;
  }

  private getErrorMessage(code?: number): string {
    const errorMessages: Record<number, string> = {
      400: 'No such account or incorrect password',
      401: 'Account disabled',
      402: 'Permission denied',
      403: 'Two-step verification code required',
      404: 'Failed to authenticate 2-step verification code',
      406: 'Enforce to authenticate with 2-step verification code',
      407: 'Blocked IP source',
      408: 'Expired password cannot change',
      409: 'Expired password',
      410: 'Password must be changed',
    };

    return errorMessages[code || 0] || `Unknown error (code: ${code})`;
  }

  setSid(sid: string) {
    this.sid = sid;
  }
}

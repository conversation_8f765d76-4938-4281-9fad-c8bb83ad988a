use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct Song {
    pub id: Option<i64>,
    pub title: String,
    pub artist: Option<String>,
    pub album: Option<String>,
    pub genre: Option<String>,
    pub year: Option<i32>,
    pub duration: i32, // in seconds
    pub file_path: String,
    pub file_size: Option<i64>,
    pub bit_rate: Option<i32>,
    pub sample_rate: Option<i32>,
    pub cover_art_path: Option<String>,
    pub date_added: Option<String>,
    pub date_modified: Option<String>,
    pub play_count: i32,
    pub last_played: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AudioMetadata {
    pub title: Option<String>,
    pub artist: Option<String>,
    pub album: Option<String>,
    pub genre: Option<String>,
    pub year: Option<i32>,
    pub duration: i32,
    pub bit_rate: Option<i32>,
    pub sample_rate: Option<i32>,
}

#[derive(Debug, <PERSON><PERSON>, Ser<PERSON><PERSON>, Deserialize)]
pub struct PlayHistory {
    pub id: Option<i64>,
    pub song_id: i64,
    pub played_at: Option<String>,
    pub play_duration: Option<i32>,
}

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct Playlist {
    pub id: Option<i64>,
    pub name: String,
    pub description: Option<String>,
    pub cover_image: Option<String>,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
    pub song_count: i32,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct PlaylistSong {
    pub id: Option<i64>,
    pub playlist_id: i64,
    pub song_id: i64,
    pub position: i32,
    pub added_at: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Settings {
    pub key: String,
    pub value: String,
    pub updated_at: Option<String>,
}

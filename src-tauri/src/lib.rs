mod commands;
mod models;

use commands::*;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .invoke_handler(tauri::generate_handler![
            get_all_songs,
            add_song,
            get_all_playlists,
            create_playlist,
            scan_music_folder,
            get_file_info,
            get_audio_metadata,
            nas_login,
            nas_list_files,
            nas_get_download_url,
            test_quickconnect_connectivity,
            scan_nas_ports
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

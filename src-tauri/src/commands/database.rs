use crate::models::{Song, Playlist};

#[tauri::command]
pub async fn get_all_songs() -> Result<Vec<Song>, String> {
    // For now, return empty list. We'll implement database operations later
    Ok(vec![])
}

#[tauri::command]
pub async fn add_song(song: Song) -> Result<Song, String> {
    // For now, just return the song with a dummy ID
    Ok(Song {
        id: Some(1),
        ..song
    })
}

#[tauri::command]
pub async fn get_all_playlists() -> Result<Vec<Playlist>, String> {
    // For now, return empty list
    Ok(vec![])
}

#[tauri::command]
pub async fn create_playlist(name: String, description: Option<String>) -> Result<Playlist, String> {
    Ok(Playlist {
        id: Some(1),
        name,
        description,
        cover_image: None,
        created_at: None,
        updated_at: None,
        song_count: 0,
    })
}

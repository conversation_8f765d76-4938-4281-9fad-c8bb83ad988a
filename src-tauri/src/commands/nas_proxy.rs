use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize)]
pub struct NASLoginRequest {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub use_https: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NASLoginResponse {
    pub success: bool,
    pub sid: Option<String>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NASFileInfo {
    pub name: String,
    pub path: String,
    pub isdir: bool,
    pub size: Option<u64>,
    pub mt_time: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NASListResponse {
    pub success: bool,
    pub files: Option<Vec<NASFileInfo>>,
    pub error: Option<String>,
}

#[tauri::command]
pub async fn nas_login(request: NASLoginRequest) -> Result<NASLoginResponse, String> {
    // 特殊处理QuickConnect地址
    let (base_url, is_quickconnect) = if request.host.contains("quickconnect.cn") {
        // QuickConnect地址，使用HTTPS和标准端口
        let quickconnect_url = if request.host.starts_with("http") {
            request.host.clone()
        } else {
            format!("https://{}", request.host)
        };
        (quickconnect_url, true)
    } else {
        // 普通地址
        let protocol = if request.use_https { "https" } else { "http" };
        (format!("{}://{}:{}", protocol, request.host, request.port), false)
    };

    println!("Connecting to: {} (QuickConnect: {})", base_url, is_quickconnect);

    // 创建HTTP客户端，QuickConnect需要更长的超时时间
    let timeout_secs = if is_quickconnect { 30 } else { 10 };
    let client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true) // 接受自签名证书
        .timeout(std::time::Duration::from_secs(timeout_secs))
        .user_agent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
        .build()
        .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

    // 准备登录参数
    let mut params = HashMap::new();
    params.insert("api", "SYNO.API.Auth");
    params.insert("version", "3");
    params.insert("method", "login");
    params.insert("account", &request.username);
    params.insert("passwd", &request.password);
    params.insert("session", "FileStation");
    params.insert("format", "sid");

    let auth_url = format!("{}/webapi/auth.cgi", base_url);

    match client.post(&auth_url)
        .form(&params)
        .send()
        .await
    {
        Ok(response) => {
            match response.json::<serde_json::Value>().await {
                Ok(data) => {
                    if data["success"].as_bool().unwrap_or(false) {
                        let sid = data["data"]["sid"].as_str().map(|s| s.to_string());
                        Ok(NASLoginResponse {
                            success: true,
                            sid,
                            error: None,
                        })
                    } else {
                        let error_code = data["error"]["code"].as_u64().unwrap_or(0);
                        let error_msg = match error_code {
                            400 => "账号或密码错误",
                            401 => "账号已禁用",
                            402 => "权限不足",
                            403 => "需要两步验证",
                            407 => "IP地址被阻止",
                            _ => "登录失败",
                        };
                        Ok(NASLoginResponse {
                            success: false,
                            sid: None,
                            error: Some(format!("{} (错误代码: {})", error_msg, error_code)),
                        })
                    }
                }
                Err(e) => Ok(NASLoginResponse {
                    success: false,
                    sid: None,
                    error: Some(format!("解析响应失败: {}", e)),
                })
            }
        }
        Err(e) => {
            let error_msg = if e.is_timeout() {
                format!("连接超时 - 请检查NAS地址和端口: {}:{}", request.host, request.port)
            } else if e.is_connect() {
                format!("无法连接到NAS - 请检查网络连接和NAS状态: {}:{}", request.host, request.port)
            } else {
                format!("网络错误: {}", e)
            };

            Ok(NASLoginResponse {
                success: false,
                sid: None,
                error: Some(error_msg),
            })
        }
    }
}

#[tauri::command]
pub async fn nas_list_files(
    host: String,
    port: u16,
    use_https: bool,
    sid: String,
    path: String,
) -> Result<NASListResponse, String> {
    let protocol = if use_https { "https" } else { "http" };
    let base_url = format!("{}://{}:{}", protocol, host, port);

    let client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true)
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

    let mut params = HashMap::new();
    params.insert("api", "SYNO.FileStation.List");
    params.insert("version", "2");
    params.insert("method", "list");
    params.insert("folder_path", &path);
    params.insert("additional", "[\"time\",\"perm\"]");
    params.insert("_sid", &sid);

    let list_url = format!("{}/webapi/entry.cgi", base_url);

    match client.post(&list_url)
        .form(&params)
        .send()
        .await
    {
        Ok(response) => {
            match response.json::<serde_json::Value>().await {
                Ok(data) => {
                    if data["success"].as_bool().unwrap_or(false) {
                        let files_data = &data["data"]["files"];
                        let mut files = Vec::new();

                        if let Some(files_array) = files_data.as_array() {
                            for file in files_array {
                                files.push(NASFileInfo {
                                    name: file["name"].as_str().unwrap_or("").to_string(),
                                    path: file["path"].as_str().unwrap_or("").to_string(),
                                    isdir: file["isdir"].as_bool().unwrap_or(false),
                                    size: file["size"].as_u64(),
                                    mt_time: file["additional"]["time"]["mtime"].as_u64(),
                                });
                            }
                        }

                        Ok(NASListResponse {
                            success: true,
                            files: Some(files),
                            error: None,
                        })
                    } else {
                        Ok(NASListResponse {
                            success: false,
                            files: None,
                            error: Some("获取文件列表失败".to_string()),
                        })
                    }
                }
                Err(e) => Ok(NASListResponse {
                    success: false,
                    files: None,
                    error: Some(format!("解析响应失败: {}", e)),
                })
            }
        }
        Err(e) => Ok(NASListResponse {
            success: false,
            files: None,
            error: Some(format!("网络错误: {}", e)),
        })
    }
}

#[tauri::command]
pub async fn nas_get_download_url(
    host: String,
    port: u16,
    use_https: bool,
    sid: String,
    file_path: String,
) -> Result<String, String> {
    let protocol = if use_https { "https" } else { "http" };
    let base_url = format!("{}://{}:{}", protocol, host, port);

    let download_url = format!(
        "{}/webapi/entry.cgi?api=SYNO.FileStation.Download&version=2&method=download&path={}&mode=download&_sid={}",
        base_url,
        urlencoding::encode(&file_path),
        sid
    );

    Ok(download_url)
}

#[tauri::command]
pub async fn test_quickconnect_connectivity(host: String) -> Result<String, String> {
    println!("Testing QuickConnect connectivity to: {}", host);

    let client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true)
        .timeout(std::time::Duration::from_secs(15))
        .user_agent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
        .build()
        .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

    // 测试不同的连接方式
    let test_urls = vec![
        format!("https://{}", host),
        format!("https://{}:5001", host),
        format!("http://{}:5000", host),
        format!("https://{}:27323", host),
    ];

    for url in test_urls {
        println!("Testing URL: {}", url);

        match client.get(&url).send().await {
            Ok(response) => {
                let status = response.status();
                println!("Response from {}: {}", url, status);

                if status.is_success() || status.as_u16() == 200 {
                    return Ok(format!("Successfully connected to: {}", url));
                } else if status.as_u16() == 302 || status.as_u16() == 301 {
                    return Ok(format!("Redirect response from: {} (this is normal for DSM)", url));
                }
            }
            Err(e) => {
                println!("Failed to connect to {}: {}", url, e);
                continue;
            }
        }
    }

    Err("All connection attempts failed".to_string())
}

#[tauri::command]
pub async fn scan_nas_ports(host: String) -> Result<Vec<u16>, String> {
    println!("Scanning ports for host: {}", host);

    let client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true)
        .timeout(std::time::Duration::from_secs(3))
        .build()
        .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

    // 常用的群晖端口
    let ports_to_scan = vec![5000, 5001, 80, 443, 8080, 8443, 27323, 7000, 7001];
    let mut open_ports = Vec::new();

    for port in ports_to_scan {
        // 测试HTTP
        let http_url = format!("http://{}:{}", host, port);
        if let Ok(response) = client.get(&http_url).send().await {
            if response.status().is_success() || response.status().as_u16() == 302 {
                println!("Found open port: {} (HTTP)", port);
                open_ports.push(port);
                continue;
            }
        }

        // 测试HTTPS
        let https_url = format!("https://{}:{}", host, port);
        if let Ok(response) = client.get(&https_url).send().await {
            if response.status().is_success() || response.status().as_u16() == 302 {
                println!("Found open port: {} (HTTPS)", port);
                open_ports.push(port);
            }
        }
    }

    println!("Scan complete. Open ports: {:?}", open_ports);
    Ok(open_ports)
}

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize)]
pub struct NASLoginRequest {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub use_https: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NASLoginResponse {
    pub success: bool,
    pub sid: Option<String>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NASFileInfo {
    pub name: String,
    pub path: String,
    pub isdir: bool,
    pub size: Option<u64>,
    pub mt_time: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NASListResponse {
    pub success: bool,
    pub files: Option<Vec<NASFileInfo>>,
    pub error: Option<String>,
}

#[tauri::command]
pub async fn nas_login(request: NASLoginRequest) -> Result<NASLoginResponse, String> {
    let protocol = if request.use_https { "https" } else { "http" };
    let base_url = format!("{}://{}:{}", protocol, request.host, request.port);
    
    // 创建HTTP客户端
    let client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true) // 接受自签名证书
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

    // 准备登录参数
    let mut params = HashMap::new();
    params.insert("api", "SYNO.API.Auth");
    params.insert("version", "3");
    params.insert("method", "login");
    params.insert("account", &request.username);
    params.insert("passwd", &request.password);
    params.insert("session", "FileStation");
    params.insert("format", "sid");

    let auth_url = format!("{}/webapi/auth.cgi", base_url);
    
    match client.post(&auth_url)
        .form(&params)
        .send()
        .await
    {
        Ok(response) => {
            match response.json::<serde_json::Value>().await {
                Ok(data) => {
                    if data["success"].as_bool().unwrap_or(false) {
                        let sid = data["data"]["sid"].as_str().map(|s| s.to_string());
                        Ok(NASLoginResponse {
                            success: true,
                            sid,
                            error: None,
                        })
                    } else {
                        let error_code = data["error"]["code"].as_u64().unwrap_or(0);
                        let error_msg = match error_code {
                            400 => "账号或密码错误",
                            401 => "账号已禁用",
                            402 => "权限不足",
                            403 => "需要两步验证",
                            407 => "IP地址被阻止",
                            _ => "登录失败",
                        };
                        Ok(NASLoginResponse {
                            success: false,
                            sid: None,
                            error: Some(format!("{} (错误代码: {})", error_msg, error_code)),
                        })
                    }
                }
                Err(e) => Ok(NASLoginResponse {
                    success: false,
                    sid: None,
                    error: Some(format!("解析响应失败: {}", e)),
                })
            }
        }
        Err(e) => {
            let error_msg = if e.is_timeout() {
                format!("连接超时 - 请检查NAS地址和端口: {}:{}", request.host, request.port)
            } else if e.is_connect() {
                format!("无法连接到NAS - 请检查网络连接和NAS状态: {}:{}", request.host, request.port)
            } else {
                format!("网络错误: {}", e)
            };
            
            Ok(NASLoginResponse {
                success: false,
                sid: None,
                error: Some(error_msg),
            })
        }
    }
}

#[tauri::command]
pub async fn nas_list_files(
    host: String,
    port: u16,
    use_https: bool,
    sid: String,
    path: String,
) -> Result<NASListResponse, String> {
    let protocol = if use_https { "https" } else { "http" };
    let base_url = format!("{}://{}:{}", protocol, host, port);
    
    let client = reqwest::Client::builder()
        .danger_accept_invalid_certs(true)
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .map_err(|e| format!("Failed to create HTTP client: {}", e))?;

    let mut params = HashMap::new();
    params.insert("api", "SYNO.FileStation.List");
    params.insert("version", "2");
    params.insert("method", "list");
    params.insert("folder_path", &path);
    params.insert("additional", "[\"time\",\"perm\"]");
    params.insert("_sid", &sid);

    let list_url = format!("{}/webapi/entry.cgi", base_url);
    
    match client.post(&list_url)
        .form(&params)
        .send()
        .await
    {
        Ok(response) => {
            match response.json::<serde_json::Value>().await {
                Ok(data) => {
                    if data["success"].as_bool().unwrap_or(false) {
                        let files_data = &data["data"]["files"];
                        let mut files = Vec::new();
                        
                        if let Some(files_array) = files_data.as_array() {
                            for file in files_array {
                                files.push(NASFileInfo {
                                    name: file["name"].as_str().unwrap_or("").to_string(),
                                    path: file["path"].as_str().unwrap_or("").to_string(),
                                    isdir: file["isdir"].as_bool().unwrap_or(false),
                                    size: file["size"].as_u64(),
                                    mt_time: file["additional"]["time"]["mtime"].as_u64(),
                                });
                            }
                        }
                        
                        Ok(NASListResponse {
                            success: true,
                            files: Some(files),
                            error: None,
                        })
                    } else {
                        Ok(NASListResponse {
                            success: false,
                            files: None,
                            error: Some("获取文件列表失败".to_string()),
                        })
                    }
                }
                Err(e) => Ok(NASListResponse {
                    success: false,
                    files: None,
                    error: Some(format!("解析响应失败: {}", e)),
                })
            }
        }
        Err(e) => Ok(NASListResponse {
            success: false,
            files: None,
            error: Some(format!("网络错误: {}", e)),
        })
    }
}

#[tauri::command]
pub async fn nas_get_download_url(
    host: String,
    port: u16,
    use_https: bool,
    sid: String,
    file_path: String,
) -> Result<String, String> {
    let protocol = if use_https { "https" } else { "http" };
    let base_url = format!("{}://{}:{}", protocol, host, port);
    
    let download_url = format!(
        "{}/webapi/entry.cgi?api=SYNO.FileStation.Download&version=2&method=download&path={}&mode=download&_sid={}",
        base_url,
        urlencoding::encode(&file_path),
        sid
    );
    
    Ok(download_url)
}

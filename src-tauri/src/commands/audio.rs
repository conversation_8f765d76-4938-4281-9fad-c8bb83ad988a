use crate::models::AudioMetadata;
use id3::Tag;
use std::path::Path;

#[tauri::command]
pub async fn get_audio_metadata(file_path: String) -> Result<AudioMetadata, String> {
    let path = Path::new(&file_path);
    
    if !path.exists() {
        return Err("File does not exist".to_string());
    }
    
    // Try to read ID3 tags for MP3 files
    if let Some(extension) = path.extension() {
        if extension.to_str() == Some("mp3") {
            if let Ok(tag) = Tag::read_from_path(&path) {
                return Ok(AudioMetadata {
                    title: tag.title().map(|s| s.to_string()),
                    artist: tag.artist().map(|s| s.to_string()),
                    album: tag.album().map(|s| s.to_string()),
                    genre: tag.genre().map(|s| s.to_string()),
                    year: tag.year().map(|y| y as i32),
                    duration: tag.duration().unwrap_or(0) as i32,
                    bit_rate: None, // Would need additional library for this
                    sample_rate: None, // Would need additional library for this
                });
            }
        }
    }
    
    // Fallback: extract filename as title
    let filename = path.file_stem()
        .and_then(|s| s.to_str())
        .unwrap_or("Unknown")
        .to_string();
    
    Ok(AudioMetadata {
        title: Some(filename),
        artist: None,
        album: None,
        genre: None,
        year: None,
        duration: 0, // Would need audio analysis library
        bit_rate: None,
        sample_rate: None,
    })
}

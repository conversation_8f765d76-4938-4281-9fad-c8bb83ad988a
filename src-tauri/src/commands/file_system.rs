use std::path::Path;
use walkdir::WalkDir;
use crate::models::Song;

#[tauri::command]
pub async fn scan_music_folder(path: String) -> Result<Vec<String>, String> {
    let music_extensions = ["mp3", "flac", "aac", "ogg", "wav", "m4a"];
    let mut music_files = Vec::new();
    
    for entry in WalkDir::new(&path).into_iter().filter_map(|e| e.ok()) {
        if entry.file_type().is_file() {
            if let Some(extension) = entry.path().extension() {
                if let Some(ext_str) = extension.to_str() {
                    if music_extensions.contains(&ext_str.to_lowercase().as_str()) {
                        if let Some(path_str) = entry.path().to_str() {
                            music_files.push(path_str.to_string());
                        }
                    }
                }
            }
        }
    }
    
    Ok(music_files)
}

#[tauri::command]
pub async fn get_file_info(file_path: String) -> Result<(u64, String), String> {
    let path = Path::new(&file_path);
    
    if !path.exists() {
        return Err("File does not exist".to_string());
    }
    
    let metadata = std::fs::metadata(&path)
        .map_err(|e| format!("Failed to get file metadata: {}", e))?;
    
    let file_size = metadata.len();
    let modified = metadata.modified()
        .map_err(|e| format!("Failed to get modification time: {}", e))?;
    
    let modified_str = format!("{:?}", modified);
    
    Ok((file_size, modified_str))
}
